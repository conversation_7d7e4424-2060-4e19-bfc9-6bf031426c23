/// 数据库通用性和兼容性测试套件
/// 测试跨平台兼容性、数据格式标准化、互操作性

use std::fs::{File, create_dir_all};
use std::io::{Write, BufWriter, Read, BufReader};
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use byteorder::{LittleEndian, BigEndian, WriteBytesExt, ReadBytesExt};
use serde::{Serialize, Deserialize};
use serde_json;
use std::collections::HashMap;

/// 标准化数据记录格式
#[derive(Debug, Clone, Serialize, Deserialize)]
struct StandardRecord {
    // 标准字段
    id: u64,
    timestamp: u64,           // Unix纳秒时间戳
    value: f64,              // IEEE 754双精度浮点数
    quality: u32,            // 数据质量标识
    
    // 元数据字段
    point_id: String,        // 测点标识
    device_id: String,       // 设备标识
    data_type: String,       // 数据类型
    unit: String,            // 单位
    
    // 扩展字段
    tags: HashMap<String, String>,  // 标签键值对
    attributes: HashMap<String, serde_json::Value>, // 属性
}

/// 数据库格式类型
#[derive(Debug, Clone)]
enum DatabaseFormat {
    Binary,          // 二进制格式
    JSON,           // JSON格式
    CSV,            // CSV格式
    Parquet,        // Parquet格式
    SQLite,         // SQLite格式
}

/// 字节序类型
#[derive(Debug, Clone)]
enum Endianness {
    Little,         // 小端序
    Big,           // 大端序
}

/// 兼容性测试器
struct CompatibilityTester {
    output_dir: String,
    test_data: Vec<StandardRecord>,
}

impl CompatibilityTester {
    fn new(output_dir: String) -> Self {
        Self {
            output_dir,
            test_data: Vec::new(),
        }
    }
    
    /// 生成标准测试数据
    fn generate_test_data(&mut self, count: usize) {
        println!("🔧 生成标准测试数据...");
        
        self.test_data.clear();
        self.test_data.reserve(count);
        
        for i in 0..count {
            let timestamp = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_nanos() as u64 + i as u64;
            
            let mut tags = HashMap::new();
            tags.insert("plant".to_string(), format!("Plant_{}", i % 3 + 1));
            tags.insert("unit".to_string(), format!("Unit_{}", i % 4 + 1));
            tags.insert("critical".to_string(), (i % 10 == 0).to_string());
            
            let mut attributes = HashMap::new();
            attributes.insert("min_value".to_string(), serde_json::Value::Number(serde_json::Number::from(0)));
            attributes.insert("max_value".to_string(), serde_json::Value::Number(serde_json::Number::from(100)));
            attributes.insert("precision".to_string(), serde_json::Value::Number(serde_json::Number::from(2)));
            
            let data_type = match i % 4 {
                0 => "temperature",
                1 => "pressure", 
                2 => "flow",
                _ => "level",
            };
            
            let unit = match data_type {
                "temperature" => "°C",
                "pressure" => "kPa",
                "flow" => "m³/h",
                _ => "m",
            };
            
            let record = StandardRecord {
                id: i as u64 + 1,
                timestamp,
                value: 25.0 + (i as f64 % 100.0),
                quality: if i % 100 < 98 { 192 } else { 0 },
                point_id: format!("{}_{:04}", data_type.to_uppercase(), i % 1000),
                device_id: format!("PLC_{}", i % 10 + 1),
                data_type: data_type.to_string(),
                unit: unit.to_string(),
                tags,
                attributes,
            };
            
            self.test_data.push(record);
        }
        
        println!("✅ 生成了 {} 条标准测试数据", count);
    }
    
    /// 测试二进制格式兼容性
    fn test_binary_format(&self, endianness: Endianness) -> Result<(), String> {
        let suffix = match endianness {
            Endianness::Little => "le",
            Endianness::Big => "be",
        };
        
        let file_path = Path::new(&self.output_dir).join(format!("test_binary_{}.db", suffix));
        println!("🔧 测试二进制格式 ({:?})...", endianness);
        
        // 写入测试
        {
            let mut writer = BufWriter::new(
                File::create(&file_path).map_err(|e| format!("创建文件失败: {}", e))?
            );
            
            // 写入文件头
            writer.write_all(b"NLKDB").map_err(|e| format!("写入魔数失败: {}", e))?;
            writer.write_u8(1).map_err(|e| format!("写入版本失败: {}", e))?; // 版本号
            writer.write_u8(match endianness {
                Endianness::Little => 0,
                Endianness::Big => 1,
            }).map_err(|e| format!("写入字节序失败: {}", e))?;
            
            // 写入记录数
            match endianness {
                Endianness::Little => writer.write_u64::<LittleEndian>(self.test_data.len() as u64),
                Endianness::Big => writer.write_u64::<BigEndian>(self.test_data.len() as u64),
            }.map_err(|e| format!("写入记录数失败: {}", e))?;
            
            // 写入记录
            for record in &self.test_data {
                self.write_binary_record(&mut writer, record, &endianness)?;
            }
            
            writer.flush().map_err(|e| format!("刷新失败: {}", e))?;
        }
        
        // 读取验证测试
        {
            let mut reader = BufReader::new(
                File::open(&file_path).map_err(|e| format!("打开文件失败: {}", e))?
            );
            
            // 验证文件头
            let mut magic = [0u8; 5];
            reader.read_exact(&mut magic).map_err(|e| format!("读取魔数失败: {}", e))?;
            if &magic != b"NLKDB" {
                return Err("魔数不匹配".to_string());
            }
            
            let version = reader.read_u8().map_err(|e| format!("读取版本失败: {}", e))?;
            let endian_flag = reader.read_u8().map_err(|e| format!("读取字节序失败: {}", e))?;
            
            let record_count = match endianness {
                Endianness::Little => reader.read_u64::<LittleEndian>(),
                Endianness::Big => reader.read_u64::<BigEndian>(),
            }.map_err(|e| format!("读取记录数失败: {}", e))?;
            
            if record_count != self.test_data.len() as u64 {
                return Err(format!("记录数不匹配: 期望 {}, 实际 {}", self.test_data.len(), record_count));
            }
            
            // 验证前10条记录
            for i in 0..std::cmp::min(10, self.test_data.len()) {
                let read_record = self.read_binary_record(&mut reader, &endianness)?;
                let original = &self.test_data[i];
                
                if read_record.id != original.id || 
                   (read_record.value - original.value).abs() > 1e-10 {
                    return Err(format!("记录 {} 数据不匹配", i));
                }
            }
        }
        
        let file_size = std::fs::metadata(&file_path)
            .map_err(|e| format!("获取文件大小失败: {}", e))?
            .len();
        
        println!("✅ 二进制格式 ({:?}) 测试通过", endianness);
        println!("   文件大小: {:.2} MB", file_size as f64 / (1024.0 * 1024.0));
        
        Ok(())
    }
    
    /// 写入二进制记录
    fn write_binary_record(&self, writer: &mut BufWriter<File>, record: &StandardRecord, endianness: &Endianness) -> Result<(), String> {
        match endianness {
            Endianness::Little => {
                writer.write_u64::<LittleEndian>(record.id).map_err(|e| e.to_string())?;
                writer.write_u64::<LittleEndian>(record.timestamp).map_err(|e| e.to_string())?;
                writer.write_f64::<LittleEndian>(record.value).map_err(|e| e.to_string())?;
                writer.write_u32::<LittleEndian>(record.quality).map_err(|e| e.to_string())?;
            }
            Endianness::Big => {
                writer.write_u64::<BigEndian>(record.id).map_err(|e| e.to_string())?;
                writer.write_u64::<BigEndian>(record.timestamp).map_err(|e| e.to_string())?;
                writer.write_f64::<BigEndian>(record.value).map_err(|e| e.to_string())?;
                writer.write_u32::<BigEndian>(record.quality).map_err(|e| e.to_string())?;
            }
        }
        
        // 写入字符串字段
        self.write_string(writer, &record.point_id, endianness)?;
        self.write_string(writer, &record.device_id, endianness)?;
        self.write_string(writer, &record.data_type, endianness)?;
        self.write_string(writer, &record.unit, endianness)?;
        
        // 写入JSON序列化的复杂字段
        let tags_json = serde_json::to_string(&record.tags)
            .map_err(|e| format!("序列化tags失败: {}", e))?;
        self.write_string(writer, &tags_json, endianness)?;
        
        let attrs_json = serde_json::to_string(&record.attributes)
            .map_err(|e| format!("序列化attributes失败: {}", e))?;
        self.write_string(writer, &attrs_json, endianness)?;
        
        Ok(())
    }
    
    /// 读取二进制记录
    fn read_binary_record(&self, reader: &mut BufReader<File>, endianness: &Endianness) -> Result<StandardRecord, String> {
        let (id, timestamp, value, quality) = match endianness {
            Endianness::Little => (
                reader.read_u64::<LittleEndian>().map_err(|e| e.to_string())?,
                reader.read_u64::<LittleEndian>().map_err(|e| e.to_string())?,
                reader.read_f64::<LittleEndian>().map_err(|e| e.to_string())?,
                reader.read_u32::<LittleEndian>().map_err(|e| e.to_string())?,
            ),
            Endianness::Big => (
                reader.read_u64::<BigEndian>().map_err(|e| e.to_string())?,
                reader.read_u64::<BigEndian>().map_err(|e| e.to_string())?,
                reader.read_f64::<BigEndian>().map_err(|e| e.to_string())?,
                reader.read_u32::<BigEndian>().map_err(|e| e.to_string())?,
            ),
        };
        
        let point_id = self.read_string(reader, endianness)?;
        let device_id = self.read_string(reader, endianness)?;
        let data_type = self.read_string(reader, endianness)?;
        let unit = self.read_string(reader, endianness)?;
        
        let tags_json = self.read_string(reader, endianness)?;
        let tags: HashMap<String, String> = serde_json::from_str(&tags_json)
            .map_err(|e| format!("反序列化tags失败: {}", e))?;
        
        let attrs_json = self.read_string(reader, endianness)?;
        let attributes: HashMap<String, serde_json::Value> = serde_json::from_str(&attrs_json)
            .map_err(|e| format!("反序列化attributes失败: {}", e))?;
        
        Ok(StandardRecord {
            id, timestamp, value, quality,
            point_id, device_id, data_type, unit,
            tags, attributes,
        })
    }
    
    /// 写入字符串
    fn write_string(&self, writer: &mut BufWriter<File>, s: &str, endianness: &Endianness) -> Result<(), String> {
        let bytes = s.as_bytes();
        match endianness {
            Endianness::Little => writer.write_u32::<LittleEndian>(bytes.len() as u32).map_err(|e| e.to_string())?,
            Endianness::Big => writer.write_u32::<BigEndian>(bytes.len() as u32).map_err(|e| e.to_string())?,
        }
        writer.write_all(bytes).map_err(|e| e.to_string())?;
        Ok(())
    }
    
    /// 读取字符串
    fn read_string(&self, reader: &mut BufReader<File>, endianness: &Endianness) -> Result<String, String> {
        let len = match endianness {
            Endianness::Little => reader.read_u32::<LittleEndian>().map_err(|e| e.to_string())?,
            Endianness::Big => reader.read_u32::<BigEndian>().map_err(|e| e.to_string())?,
        };

        let mut bytes = vec![0u8; len as usize];
        reader.read_exact(&mut bytes).map_err(|e| e.to_string())?;
        String::from_utf8(bytes).map_err(|e| format!("UTF-8解码失败: {}", e))
    }
    
    /// 测试JSON格式兼容性
    fn test_json_format(&self) -> Result<(), String> {
        let file_path = Path::new(&self.output_dir).join("test_data.json");
        println!("🔧 测试JSON格式兼容性...");
        
        // 写入测试
        {
            let mut writer = BufWriter::new(
                File::create(&file_path).map_err(|e| format!("创建JSON文件失败: {}", e))?
            );
            
            // 写入JSON数组
            writeln!(writer, "[").map_err(|e| e.to_string())?;
            for (i, record) in self.test_data.iter().enumerate() {
                let json_str = serde_json::to_string_pretty(record)
                    .map_err(|e| format!("JSON序列化失败: {}", e))?;

                if i > 0 {
                    writeln!(writer, ",").map_err(|e| e.to_string())?;
                }
                write!(writer, "{}", json_str).map_err(|e| e.to_string())?;
            }
            writeln!(writer, "\n]").map_err(|e| e.to_string())?;

            writer.flush().map_err(|e| e.to_string())?;
        }
        
        // 读取验证测试
        {
            let content = std::fs::read_to_string(&file_path)
                .map_err(|e| format!("读取JSON文件失败: {}", e))?;
            
            let parsed_records: Vec<StandardRecord> = serde_json::from_str(&content)
                .map_err(|e| format!("JSON反序列化失败: {}", e))?;
            
            if parsed_records.len() != self.test_data.len() {
                return Err("JSON记录数不匹配".to_string());
            }
            
            // 验证前5条记录
            for i in 0..std::cmp::min(5, self.test_data.len()) {
                if parsed_records[i].id != self.test_data[i].id {
                    return Err(format!("JSON记录 {} ID不匹配", i));
                }
            }
        }
        
        let file_size = std::fs::metadata(&file_path)
            .map_err(|e| format!("获取JSON文件大小失败: {}", e))?
            .len();
        
        println!("✅ JSON格式测试通过");
        println!("   文件大小: {:.2} MB", file_size as f64 / (1024.0 * 1024.0));
        
        Ok(())
    }
    
    /// 测试CSV格式兼容性
    fn test_csv_format(&self) -> Result<(), String> {
        let file_path = Path::new(&self.output_dir).join("test_data.csv");
        println!("🔧 测试CSV格式兼容性...");
        
        // 写入测试
        {
            let mut writer = BufWriter::new(
                File::create(&file_path).map_err(|e| format!("创建CSV文件失败: {}", e))?
            );
            
            // 写入CSV头
            writeln!(writer, "id,timestamp,value,quality,point_id,device_id,data_type,unit,tags,attributes").map_err(|e| e.to_string())?;

            // 写入数据行
            for record in &self.test_data {
                let tags_json = serde_json::to_string(&record.tags)
                    .map_err(|e| format!("序列化tags失败: {}", e))?;
                let attrs_json = serde_json::to_string(&record.attributes)
                    .map_err(|e| format!("序列化attributes失败: {}", e))?;

                writeln!(writer, "{},{},{},{},{},{},{},{},\"{}\",\"{}\"",
                    record.id,
                    record.timestamp,
                    record.value,
                    record.quality,
                    record.point_id,
                    record.device_id,
                    record.data_type,
                    record.unit,
                    tags_json.replace("\"", "\"\""), // CSV转义
                    attrs_json.replace("\"", "\"\"")
                ).map_err(|e| e.to_string())?;
            }

            writer.flush().map_err(|e| e.to_string())?;
        }
        
        let file_size = std::fs::metadata(&file_path)
            .map_err(|e| format!("获取CSV文件大小失败: {}", e))?
            .len();
        
        println!("✅ CSV格式测试通过");
        println!("   文件大小: {:.2} MB", file_size as f64 / (1024.0 * 1024.0));
        
        Ok(())
    }
    
    /// 运行完整兼容性测试
    pub fn run_compatibility_tests(&mut self, record_count: usize) -> Result<(), String> {
        println!("🧪 数据库通用性和兼容性测试");
        println!("{}", "=".repeat(60));
        
        create_dir_all(&self.output_dir)
            .map_err(|e| format!("创建目录失败: {}", e))?;
        
        // 生成测试数据
        self.generate_test_data(record_count);
        
        println!("\n📊 测试配置:");
        println!("   测试记录数: {}", record_count);
        println!("   输出目录: {}", self.output_dir);
        
        // 测试各种格式
        println!("\n🔬 格式兼容性测试:");
        
        // 二进制格式测试
        self.test_binary_format(Endianness::Little)?;
        self.test_binary_format(Endianness::Big)?;
        
        // JSON格式测试
        self.test_json_format()?;
        
        // CSV格式测试
        self.test_csv_format()?;
        
        println!("\n📈 兼容性测试总结:");
        self.print_compatibility_summary()?;
        
        Ok(())
    }
    
    /// 打印兼容性总结
    fn print_compatibility_summary(&self) -> Result<(), String> {
        println!("✅ 所有格式测试通过");
        println!("🌐 跨平台兼容性:");
        println!("   • 小端序二进制格式 (x86/x64)");
        println!("   • 大端序二进制格式 (SPARC/PowerPC)");
        println!("   • UTF-8编码支持");
        println!("   • IEEE 754浮点数标准");
        
        println!("📄 数据交换格式:");
        println!("   • JSON (Web/API兼容)");
        println!("   • CSV (Excel/分析工具兼容)");
        println!("   • 二进制 (高性能应用)");
        
        println!("🔧 互操作性特性:");
        println!("   • 标准化时间戳 (Unix纳秒)");
        println!("   • 结构化元数据");
        println!("   • 可扩展属性系统");
        println!("   • 多语言支持");
        
        Ok(())
    }
}

fn main() {
    println!("🧪 数据库通用性测试套件");
    println!("{}", "=".repeat(70));
    
    let output_dir = r"D:\database\compatibility_test".to_string();
    let record_count = 10_000; // 1万条测试数据
    
    let mut tester = CompatibilityTester::new(output_dir);
    
    match tester.run_compatibility_tests(record_count) {
        Ok(()) => {
            println!("\n🎉 所有兼容性测试通过!");
            println!("\n💡 建议:");
            println!("   • 生产环境推荐使用小端序二进制格式");
            println!("   • 数据交换推荐使用JSON格式");
            println!("   • 分析工具推荐使用CSV格式");
            println!("   • 跨平台部署已验证兼容");
        }
        Err(e) => {
            eprintln!("❌ 兼容性测试失败: {}", e);
            std::process::exit(1);
        }
    }
}
