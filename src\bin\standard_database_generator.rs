/// 标准版数据库生成器
/// 生成标准的nclink_v2.duckdb和nclink_v2.duckdb.wal文件

use std::fs::{File, create_dir_all};
use std::io::{Write, BufWriter};
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use byteorder::{LittleEndian, WriteBytesExt};
use serde_json::json;
use rand::Rng;

/// 标准数据记录
#[derive(Debug, Clone)]
struct StandardRecord {
    id: u64,
    timestamp: u64,
    point_id: String,
    value: f64,
    quality: u32,
    device_id: String,
    data_type: String,
    unit: String,
    plant_id: u32,
    tags: String,
}

/// 标准数据库生成器
struct StandardDatabaseGenerator {
    output_dir: String,
}

impl StandardDatabaseGenerator {
    fn new(output_dir: String) -> Self {
        Self { output_dir }
    }
    
    /// 生成标准工程数据记录
    fn generate_standard_record(&self, index: usize) -> StandardRecord {
        let mut rng = rand::thread_rng();
        
        // 工程数据类型
        let data_types = ["TI", "PI", "FI", "LI", "AI", "DI"];
        let data_type = data_types[index % data_types.len()];
        
        // 设备和工厂信息
        let plant_id = (index % 3) + 1;
        let unit_id = (index % 5) + 1;
        let device_id = format!("PLC_{:02}", (index % 10) + 1);
        
        // 测点标识
        let point_id = format!("{}_{:02}_{:02}_{:04}", 
                              data_type, plant_id, unit_id, index % 1000);
        
        // 时间戳 (当前时间 + 索引偏移)
        let base_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_nanos() as u64;
        let timestamp = base_time + (index as u64 * 1_000_000_000); // 每秒一条
        
        // 根据数据类型生成相应的数值和单位
        let (value, unit) = match data_type {
            "TI" => (20.0 + rng.gen::<f64>() * 80.0, "°C"),      // 温度 20-100°C
            "PI" => (0.0 + rng.gen::<f64>() * 1000.0, "kPa"),   // 压力 0-1000kPa
            "FI" => (0.0 + rng.gen::<f64>() * 500.0, "m³/h"),   // 流量 0-500m³/h
            "LI" => (0.0 + rng.gen::<f64>() * 10.0, "m"),       // 液位 0-10m
            "AI" => (4.0 + rng.gen::<f64>() * 16.0, "mA"),      // 模拟量 4-20mA
            "DI" => (if rng.gen_bool(0.7) { 1.0 } else { 0.0 }, ""), // 数字量 0/1
            _ => (0.0, ""),
        };
        
        // 数据质量 (98%好质量)
        let quality = if rng.gen_bool(0.98) { 192 } else { 0 };
        
        // 标签信息
        let tags = json!({
            "plant": format!("Plant_{}", plant_id),
            "unit": format!("Unit_{}", unit_id),
            "type": data_type,
            "critical": index % 20 == 0,
            "description": format!("{} measurement point", data_type),
            "location": format!("Area_{}", (index % 8) + 1)
        }).to_string();
        
        StandardRecord {
            id: index as u64 + 1,
            timestamp,
            point_id,
            value,
            quality,
            device_id,
            data_type: data_type.to_string(),
            unit: unit.to_string(),
            plant_id: plant_id as u32,
            tags,
        }
    }
    
    /// 序列化记录为二进制格式
    fn serialize_record(&self, record: &StandardRecord) -> Vec<u8> {
        let mut data = Vec::with_capacity(512);
        
        // 写入固定长度字段
        data.extend_from_slice(&record.id.to_le_bytes());
        data.extend_from_slice(&record.timestamp.to_le_bytes());
        data.extend_from_slice(&record.value.to_le_bytes());
        data.extend_from_slice(&record.quality.to_le_bytes());
        data.extend_from_slice(&record.plant_id.to_le_bytes());
        
        // 写入变长字符串字段
        self.write_string(&mut data, &record.point_id);
        self.write_string(&mut data, &record.device_id);
        self.write_string(&mut data, &record.data_type);
        self.write_string(&mut data, &record.unit);
        self.write_string(&mut data, &record.tags);
        
        data
    }
    
    /// 写入字符串到缓冲区
    fn write_string(&self, buffer: &mut Vec<u8>, s: &str) {
        let bytes = s.as_bytes();
        buffer.extend_from_slice(&(bytes.len() as u32).to_le_bytes());
        buffer.extend_from_slice(bytes);
    }
    
    /// 生成标准数据库文件
    pub fn generate_standard_database(&self, record_count: usize) -> Result<(), String> {
        println!("🏭 生成标准版nclink_v2数据库");
        println!("{}", "=".repeat(50));
        
        // 创建输出目录
        create_dir_all(&self.output_dir)
            .map_err(|e| format!("创建目录失败: {}", e))?;
        
        let db_path = Path::new(&self.output_dir).join("nclink_v2.duckdb");
        let wal_path = Path::new(&self.output_dir).join("nclink_v2.duckdb.wal");
        
        println!("📊 生成配置:");
        println!("   输出目录: {}", self.output_dir);
        println!("   记录数量: {}", format_number(record_count));
        println!("   数据库文件: nclink_v2.duckdb");
        println!("   WAL文件: nclink_v2.duckdb.wal");
        
        // 创建数据库文件写入器
        let mut db_writer = BufWriter::with_capacity(
            1024 * 1024, // 1MB缓冲区
            File::create(&db_path).map_err(|e| format!("创建数据库文件失败: {}", e))?
        );
        
        // 创建WAL文件写入器
        let mut wal_writer = BufWriter::with_capacity(
            256 * 1024, // 256KB缓冲区
            File::create(&wal_path).map_err(|e| format!("创建WAL文件失败: {}", e))?
        );
        
        // 写入数据库文件头
        db_writer.write_all(b"NLKDB").map_err(|e| format!("写入魔数失败: {}", e))?;
        db_writer.write_u8(1).map_err(|e| format!("写入版本失败: {}", e))?; // 版本号
        db_writer.write_u8(0).map_err(|e| format!("写入字节序失败: {}", e))?; // 小端序
        db_writer.write_u64::<LittleEndian>(record_count as u64)
            .map_err(|e| format!("写入记录数失败: {}", e))?;
        
        // 写入WAL文件头
        wal_writer.write_all(b"NLKWAL").map_err(|e| format!("写入WAL魔数失败: {}", e))?;
        wal_writer.write_u8(1).map_err(|e| format!("写入WAL版本失败: {}", e))?;
        wal_writer.write_u64::<LittleEndian>(record_count as u64)
            .map_err(|e| format!("写入WAL记录数失败: {}", e))?;
        
        println!("\n⚡ 开始生成数据...");
        
        let start_time = std::time::Instant::now();
        
        // 生成并写入记录
        for i in 0..record_count {
            // 生成记录
            let record = self.generate_standard_record(i);
            
            // 序列化并写入数据库文件
            let data = self.serialize_record(&record);
            db_writer.write_all(&data)
                .map_err(|e| format!("写入数据记录失败: {}", e))?;
            
            // 写入WAL条目
            wal_writer.write_u64::<LittleEndian>(record.timestamp)
                .map_err(|e| format!("写入WAL时间戳失败: {}", e))?;
            wal_writer.write_u64::<LittleEndian>(record.id)
                .map_err(|e| format!("写入WAL ID失败: {}", e))?;
            wal_writer.write_u8(1) // 操作类型: 1=INSERT
                .map_err(|e| format!("写入WAL操作类型失败: {}", e))?;
            
            // 定期刷新和进度报告
            if i % 10000 == 0 {
                db_writer.flush().map_err(|e| format!("刷新数据库文件失败: {}", e))?;
                wal_writer.flush().map_err(|e| format!("刷新WAL文件失败: {}", e))?;
                
                if i > 0 {
                    let elapsed = start_time.elapsed().as_secs_f64();
                    let rate = i as f64 / elapsed;
                    let eta = (record_count - i) as f64 / rate;
                    println!("📊 进度: {} 条 ({:.1}%) | 速度: {:.0} 条/秒 | ETA: {:.1}秒",
                             format_number(i + 1),
                             (i + 1) as f64 / record_count as f64 * 100.0,
                             rate,
                             eta);
                }
            }
        }
        
        // 最终刷新
        db_writer.flush().map_err(|e| format!("最终刷新数据库文件失败: {}", e))?;
        wal_writer.flush().map_err(|e| format!("最终刷新WAL文件失败: {}", e))?;
        
        let total_time = start_time.elapsed();
        
        // 获取文件大小
        let db_size = std::fs::metadata(&db_path)
            .map_err(|e| format!("获取数据库文件大小失败: {}", e))?
            .len();
        let wal_size = std::fs::metadata(&wal_path)
            .map_err(|e| format!("获取WAL文件大小失败: {}", e))?
            .len();
        
        println!("\n✅ 标准数据库生成完成!");
        println!("📈 生成统计:");
        println!("   总记录数: {}", format_number(record_count));
        println!("   总耗时: {:.2} 秒", total_time.as_secs_f64());
        println!("   生成速度: {:.0} 条/秒", record_count as f64 / total_time.as_secs_f64());
        
        println!("\n💾 文件信息:");
        println!("   📄 nclink_v2.duckdb: {:.2} MB", db_size as f64 / (1024.0 * 1024.0));
        println!("   📄 nclink_v2.duckdb.wal: {:.2} MB", wal_size as f64 / (1024.0 * 1024.0));
        println!("   📄 总大小: {:.2} MB", (db_size + wal_size) as f64 / (1024.0 * 1024.0));
        
        println!("\n🎯 数据特点:");
        println!("   • 6种工程数据类型 (TI/PI/FI/LI/AI/DI)");
        println!("   • 3个工厂，每工厂5个单元");
        println!("   • 10个PLC设备");
        println!("   • 98%数据质量良好");
        println!("   • 完整的元数据标签");
        println!("   • 标准化时间戳");
        
        Ok(())
    }
}

/// 格式化数字
fn format_number(n: usize) -> String {
    let s = n.to_string();
    let mut result = String::new();
    for (i, c) in s.chars().rev().enumerate() {
        if i > 0 && i % 3 == 0 {
            result.push(',');
        }
        result.push(c);
    }
    result.chars().rev().collect()
}

fn main() {
    println!("🏭 标准版nclink_v2数据库生成器");
    println!("{}", "=".repeat(60));
    
    let output_dir = r"D:\database".to_string();
    let record_count = 100_000; // 10万条标准记录
    
    let generator = StandardDatabaseGenerator::new(output_dir);
    
    match generator.generate_standard_database(record_count) {
        Ok(()) => {
            println!("\n🎉 标准数据库生成成功!");
            println!("\n💡 下一步:");
            println!("   • 使用Python脚本连接数据库");
            println!("   • 执行CRUD操作测试");
            println!("   • 验证数据完整性");
        }
        Err(e) => {
            eprintln!("❌ 生成失败: {}", e);
            std::process::exit(1);
        }
    }
}
