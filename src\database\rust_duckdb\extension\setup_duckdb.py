#!/usr/bin/env python3
"""
DuckDB环境设置脚本
自动安装DuckDB并验证环境
"""

import subprocess
import sys
import os
from pathlib import Path

def install_duckdb():
    """安装DuckDB Python库"""
    print("🦆 安装DuckDB Python库...")
    
    try:
        # 尝试导入DuckDB
        import duckdb
        print("✅ DuckDB已安装")
        print(f"   版本: {duckdb.__version__}")
        return True
    except ImportError:
        print("📦 DuckDB未安装，正在安装...")
        
        try:
            # 安装DuckDB
            subprocess.check_call([sys.executable, "-m", "pip", "install", "duckdb"])
            print("✅ DuckDB安装成功")
            
            # 验证安装
            import duckdb
            print(f"   版本: {duckdb.__version__}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ DuckDB安装失败: {e}")
            return False
        except ImportError:
            print("❌ DuckDB安装后仍无法导入")
            return False

def install_dependencies():
    """安装其他依赖"""
    dependencies = [
        "pandas",
        "numpy", 
        "openpyxl"  # Excel支持
    ]
    
    print("📦 检查并安装依赖包...")
    
    for package in dependencies:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 安装 {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package} 安装失败: {e}")

def test_duckdb_functionality():
    """测试DuckDB功能"""
    print("\n🧪 测试DuckDB功能...")
    
    try:
        import duckdb
        import pandas as pd
        
        # 创建测试连接
        conn = duckdb.connect(':memory:')
        
        # 创建测试表
        conn.execute("""
            CREATE TABLE test_table (
                id INTEGER,
                name VARCHAR,
                value DOUBLE
            )
        """)
        
        # 插入测试数据
        test_data = [
            (1, 'test1', 10.5),
            (2, 'test2', 20.3),
            (3, 'test3', 30.7)
        ]
        
        conn.executemany("INSERT INTO test_table VALUES (?, ?, ?)", test_data)
        
        # 查询测试
        result = conn.execute("SELECT * FROM test_table ORDER BY id").fetchall()
        print(f"✅ 基础查询测试通过: {len(result)} 条记录")
        
        # Pandas集成测试
        df = conn.execute("SELECT * FROM test_table").df()
        print(f"✅ Pandas集成测试通过: DataFrame形状 {df.shape}")
        
        # 聚合查询测试
        agg_result = conn.execute("SELECT COUNT(*), AVG(value) FROM test_table").fetchone()
        print(f"✅ 聚合查询测试通过: 记录数={agg_result[0]}, 平均值={agg_result[1]:.2f}")
        
        conn.close()
        print("✅ 所有DuckDB功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ DuckDB功能测试失败: {e}")
        return False

def create_duckdb_database():
    """创建标准DuckDB数据库"""
    print("\n🗄️ 创建标准DuckDB数据库...")
    
    try:
        import duckdb
        
        # 连接到文件数据库
        db_path = r"D:\database\nclink_v2_standard.duckdb"
        conn = duckdb.connect(db_path)
        
        # 创建表结构
        conn.execute("""
            CREATE TABLE IF NOT EXISTS nclink_data (
                id BIGINT PRIMARY KEY,
                timestamp BIGINT NOT NULL,
                point_id VARCHAR NOT NULL,
                value DOUBLE NOT NULL,
                quality INTEGER NOT NULL,
                device_id VARCHAR NOT NULL,
                data_type VARCHAR NOT NULL,
                unit VARCHAR NOT NULL,
                plant_id INTEGER NOT NULL,
                tags VARCHAR NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 插入示例数据
        sample_data = []
        import time
        import json
        
        base_time = int(time.time() * 1_000_000_000)
        
        for i in range(1000):
            data_types = ['TI', 'PI', 'FI', 'LI', 'AI', 'DI']
            data_type = data_types[i % len(data_types)]
            
            # 根据类型生成数值
            if data_type == 'TI':
                value = 20.0 + (i % 80)  # 温度 20-100°C
                unit = '°C'
            elif data_type == 'PI':
                value = i % 1000  # 压力 0-1000kPa
                unit = 'kPa'
            elif data_type == 'FI':
                value = i % 500  # 流量 0-500m³/h
                unit = 'm³/h'
            elif data_type == 'LI':
                value = (i % 100) / 10.0  # 液位 0-10m
                unit = 'm'
            elif data_type == 'AI':
                value = 4.0 + (i % 16)  # 模拟量 4-20mA
                unit = 'mA'
            else:  # DI
                value = i % 2  # 数字量 0/1
                unit = ''
            
            tags = json.dumps({
                'plant': f'Plant_{(i % 3) + 1}',
                'unit': f'Unit_{(i % 5) + 1}',
                'type': data_type
            })
            
            sample_data.append((
                i + 1,  # id
                base_time + i * 1_000_000_000,  # timestamp (每秒一条)
                f'{data_type}_{(i % 3) + 1:02d}_{(i % 5) + 1:02d}_{i % 100:04d}',  # point_id
                value,
                192 if i % 100 < 98 else 0,  # quality (98%好质量)
                f'PLC_{(i % 10) + 1:02d}',  # device_id
                data_type,
                unit,
                (i % 3) + 1,  # plant_id
                tags
            ))
        
        # 批量插入
        conn.executemany("""
            INSERT INTO nclink_data 
            (id, timestamp, point_id, value, quality, device_id, data_type, unit, plant_id, tags)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, sample_data)
        
        # 创建索引
        conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON nclink_data(timestamp)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_point_id ON nclink_data(point_id)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_device_id ON nclink_data(device_id)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_data_type ON nclink_data(data_type)")
        
        # 验证数据
        count = conn.execute("SELECT COUNT(*) FROM nclink_data").fetchone()[0]
        print(f"✅ 标准DuckDB数据库创建成功: {count} 条记录")
        print(f"   文件路径: {db_path}")
        
        # 显示统计信息
        stats = conn.execute("""
            SELECT 
                data_type,
                COUNT(*) as count,
                AVG(value) as avg_value
            FROM nclink_data 
            GROUP BY data_type 
            ORDER BY data_type
        """).fetchall()
        
        print("📊 数据统计:")
        for stat in stats:
            print(f"   {stat[0]}: {stat[1]} 条, 平均值 {stat[2]:.2f}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建DuckDB数据库失败: {e}")
        return False

def main():
    """主函数"""
    print("🦆 DuckDB环境设置")
    print("=" * 50)
    
    # 1. 安装DuckDB
    if not install_duckdb():
        print("❌ DuckDB安装失败，退出")
        return False
    
    # 2. 安装依赖
    install_dependencies()
    
    # 3. 测试功能
    if not test_duckdb_functionality():
        print("❌ DuckDB功能测试失败")
        return False
    
    # 4. 创建标准数据库
    if not create_duckdb_database():
        print("❌ 创建标准数据库失败")
        return False
    
    print("\n🎉 DuckDB环境设置完成!")
    print("\n💡 下一步:")
    print("   • 运行: python nclink_v2_duckdb.py")
    print("   • 体验DuckDB原生高性能操作")
    print("   • 使用SQL进行复杂分析")
    
    return True

if __name__ == "__main__":
    main()
