[package]
name = "nclink-v2-rust"
version = "0.1.0"
edition = "2021"
authors = ["NC-Link Team"]
description = "NC-Link V2 - Complete Python feature implementation in Rust"
license = "MIT"

[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["rt-multi-thread", "net", "io-util", "time", "macros", "signal", "sync"] }

# Web框架
axum = { version = "0.7", features = ["ws"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors"] }

# 数据库 - 使用我们的Rust DuckDB实现（基于SQLite）
rusqlite = { version = "0.31", features = ["bundled"] }
sqlparser = "0.43"

# DuckDB Rust实现所需依赖
arrow = { version = "55.0", features = ["csv", "json", "ipc"] }
parquet = { version = "55.0", features = ["arrow", "async"] }
csv = "1.3"
lz4 = "1.24"
zstd = "0.13"
snap = "1.1"
crossbeam = "0.8"
rayon = "1.8"
num-traits = "0.2"
num-bigint = "0.4"
wide = "0.7"
nom = "7.1"
byteorder = "1.5"
tempfile = "3.8"
rand = "0.8"

# 压缩算法
flate2 = "1.0"
lz4_flex = "0.11"

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sha2 = "0.10"

# 日志系统
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "fmt"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# WebSocket
tokio-tungstenite = "0.21"

# 系统信息
sysinfo = "0.29"

# 编码
hex = "0.4"

# 并发
parking_lot = "0.12"
dashmap = "5.0"

# 异步特性
async-trait = "0.1"

# 时间处理 - 使用最新版本
chrono = { version = "0.4", features = ["serde"] }

# 命令行参数解析
clap = { version = "4.0", features = ["derive"] }

# CPU核心数检测
num_cpus = "1.0"

# UUID生成
uuid = { version = "1.0", features = ["v4", "serde"] }

# 哈希计算
md5 = "0.7"

# YAML处理
serde_yaml = "0.9"

# gRPC相关
tonic = "0.11"
tonic-build = "0.11"
prost = "0.12"

# WebSocket (已在上面定义)
futures-util = "0.3"

# 调度器
tokio-cron-scheduler = "0.9"

# SSL/TLS
rustls = "0.22"
tokio-rustls = "0.25"

# 证书处理
x509-parser = "0.15"
rcgen = "0.12"

# 网络
socket2 = "0.5"

# 消息格式
bincode = "1.3"

# 正则表达式
regex = "1.10"

[build-dependencies]
tonic-build = "0.11"

# 平台特定依赖
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winuser", "processthreadsapi"] }

[features]
default = []

[[bin]]
name = "nclink-v2"
path = "src/main.rs"

[[bin]]
name = "test_grpc"
path = "src/bin/test_grpc.rs"

[[bin]]
name = "init_duckdb"
path = "src/bin/init_duckdb.rs"

[[bin]]
name = "test_duckdb_core"
path = "src/bin/test_duckdb_core.rs"

[[bin]]
name = "test_cross_platform"
path = "src/bin/test_cross_platform.rs"

[[bin]]
name = "data_writer"
path = "src/bin/data_writer.rs"

[[bin]]
name = "compressed_data_writer"
path = "src/bin/compressed_data_writer.rs"

[[bin]]
name = "ultra_compression_test"
path = "src/bin/ultra_compression_test.rs"

[[bin]]
name = "duckdb_compiler"
path = "src/bin/duckdb_compiler.rs"

[[bin]]
name = "nclink_v2_generator"
path = "src/bin/nclink_v2_generator.rs"

[[bin]]
name = "realtime_optimized_generator"
path = "src/bin/realtime_optimized_generator.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1

[profile.dev]
opt-level = 0
debug = true
