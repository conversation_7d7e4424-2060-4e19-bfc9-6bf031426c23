#!/usr/bin/env node
/**
 * Node.js DuckDB文件读取器
 * 读取nclink_v2.duckdb和nclink_v2.duckdb.wal文件
 */

const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');

class DuckDBReader {
    constructor(dbPath) {
        this.dbPath = dbPath;
        this.db = null;
    }

    // 连接数据库
    connect() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('✅ 连接成功');
                    resolve();
                }
            });
        });
    }

    // 读取文件头信息
    readHeader() {
        try {
            const buffer = fs.readFileSync(this.dbPath);
            
            // 读取DuckDB文件头
            const magic = buffer.slice(0, 8);
            const version = buffer.readUInt32LE(8);
            const pageSize = buffer.readUInt32LE(12);
            const compressionType = buffer.readUInt32LE(16);
            const createdAt = buffer.readBigUInt64LE(20);
            const modifiedAt = buffer.readBigUInt64LE(28);
            const recordCount = buffer.readBigUInt64LE(36);
            const compressionRatio = buffer.readDoubleLE(44);

            const compressionTypes = ['None', 'LZ4', 'Gzip', 'Snappy', 'ZSTD'];

            return {
                version,
                pageSize,
                compressionType: compressionTypes[compressionType] || 'Unknown',
                createdAt: new Date(Number(createdAt) * 1000).toISOString(),
                recordCount: Number(recordCount),
                compressionRatio
            };
        } catch (error) {
            console.error('读取文件头失败:', error);
            return null;
        }
    }

    // 执行查询
    query(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // 获取所有数据
    async getAllData(limit = 1000) {
        return await this.query(`SELECT * FROM engineering_data LIMIT ${limit}`);
    }

    // 按设备查询
    async getByDevice(deviceId, limit = 100) {
        return await this.query(
            'SELECT * FROM engineering_data WHERE device_id = ? ORDER BY timestamp DESC LIMIT ?',
            [deviceId, limit]
        );
    }

    // 按测点类型查询
    async getByPointType(pointType, limit = 100) {
        return await this.query(
            `SELECT * FROM engineering_data WHERE point_id LIKE '%${pointType}%' ORDER BY timestamp DESC LIMIT ?`,
            [limit]
        );
    }

    // 获取统计信息
    async getStatistics() {
        const result = await this.query(`
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT point_id) as unique_points,
                COUNT(DISTINCT device_id) as unique_devices,
                MIN(timestamp) as start_time,
                MAX(timestamp) as end_time,
                AVG(value) as avg_value
            FROM engineering_data
        `);
        return result[0];
    }

    // 关闭连接
    close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('关闭数据库失败:', err);
                    } else {
                        console.log('🔒 数据库已关闭');
                    }
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

// 使用示例
async function main() {
    const dbPath = 'engineering_data.db'; // 或 'nclink_v2.duckdb'
    const reader = new DuckDBReader(dbPath);

    try {
        // 1. 连接数据库
        await reader.connect();

        // 2. 读取文件头
        const header = reader.readHeader();
        if (header) {
            console.log(`📊 记录数: ${header.recordCount.toLocaleString()}`);
            console.log(`🗜️ 压缩比: ${header.compressionRatio.toFixed(1)}:1`);
            console.log(`📅 创建时间: ${header.createdAt}`);
        }

        // 3. 获取统计信息
        const stats = await reader.getStatistics();
        console.log(`📈 总记录: ${stats.total_records.toLocaleString()}`);
        console.log(`🔧 测点数: ${stats.unique_points.toLocaleString()}`);
        console.log(`🖥️ 设备数: ${stats.unique_devices.toLocaleString()}`);

        // 4. 查询最新数据
        const recentData = await reader.getAllData(5);
        console.log('\n📋 最新5条记录:');
        recentData.forEach(record => {
            const dt = new Date(record.timestamp * 1000);
            console.log(`  ${record.point_id}: ${record.value.toFixed(2)} @ ${dt.toISOString()}`);
        });

        // 5. 按设备查询
        const deviceData = await reader.getByDevice('Plant_A_Unit_01_Reactor_PLC', 3);
        console.log(`\n🖥️ 设备数据 (${deviceData.length} 条):`);
        deviceData.forEach(record => {
            console.log(`  ${record.point_id}: ${record.value.toFixed(2)}`);
        });

        // 6. 按测点类型查询
        const tempData = await reader.getByPointType('TI', 3);
        console.log(`\n🌡️ 温度测点 (${tempData.length} 条):`);
        tempData.forEach(record => {
            console.log(`  ${record.point_id}: ${record.value.toFixed(2)}°C`);
        });

    } catch (error) {
        console.error('❌ 操作失败:', error);
    } finally {
        await reader.close();
    }
}

// 导出类供其他模块使用
module.exports = DuckDBReader;

// 如果直接运行此文件，执行示例
if (require.main === module) {
    main();
}

/*
安装依赖:
npm install sqlite3

使用方法:
const DuckDBReader = require('./duckdb_reader');

const reader = new DuckDBReader('nclink_v2.duckdb');
await reader.connect();
const data = await reader.getAllData(100);
await reader.close();
*/
