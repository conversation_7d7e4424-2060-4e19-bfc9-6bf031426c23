#!/usr/bin/env python3
"""
nclink_v2.duckdb 高级数据分析工具
提供完整的数据分析、可视化和报告功能
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
from pathlib import Path
from nclink_v2_connector import NclinkV2Connector

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class NclinkV2Analyzer:
    """nclink_v2.duckdb 高级分析器"""
    
    def __init__(self, db_path: str = "nclink_v2.duckdb"):
        """初始化分析器"""
        self.db_path = db_path
        self.connector = NclinkV2Connector(db_path)
        
    def connect(self) -> bool:
        """连接数据库"""
        return self.connector.connect()
    
    def generate_comprehensive_report(self, output_dir: str = "analysis_reports") -> None:
        """生成综合分析报告"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        print("📊 生成综合分析报告...")
        print("=" * 40)
        
        if not self.connector.is_connected:
            if not self.connect():
                print("❌ 无法连接数据库")
                return
        
        # 1. 基础统计报告
        self._generate_basic_statistics_report(output_path)
        
        # 2. 数据质量分析
        self._generate_data_quality_report(output_path)
        
        # 3. 时间序列分析
        self._generate_time_series_analysis(output_path)
        
        # 4. 设备性能分析
        self._generate_device_performance_analysis(output_path)
        
        # 5. 测点类型分析
        self._generate_point_type_analysis(output_path)
        
        # 6. 异常检测报告
        self._generate_anomaly_detection_report(output_path)
        
        print(f"\n✅ 报告生成完成，保存在: {output_path}")
    
    def _generate_basic_statistics_report(self, output_path: Path) -> None:
        """生成基础统计报告"""
        print("📈 生成基础统计报告...")
        
        try:
            # 获取基础统计
            stats = self.connector.get_statistics()
            db_info = self.connector.get_database_info()
            
            # 创建统计图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('nclink_v2.duckdb 基础统计报告', fontsize=16, fontweight='bold')
            
            # 1. 数据量分布
            if 'top_devices' in stats:
                devices = list(stats['top_devices'].keys())
                counts = list(stats['top_devices'].values())
                
                axes[0, 0].bar(devices, counts)
                axes[0, 0].set_title('设备数据量分布')
                axes[0, 0].set_xlabel('设备ID')
                axes[0, 0].set_ylabel('记录数')
                axes[0, 0].tick_params(axis='x', rotation=45)
            
            # 2. 质量码分布
            if 'quality_distribution' in stats:
                quality_names = {192: 'Good', 64: 'Uncertain', 0: 'Bad', 68: 'Sensor Failure', 72: 'Out of Range'}
                qualities = []
                counts = []
                
                for quality, count in stats['quality_distribution'].items():
                    qualities.append(quality_names.get(quality, f'Quality {quality}'))
                    counts.append(count)
                
                axes[0, 1].pie(counts, labels=qualities, autopct='%1.1f%%')
                axes[0, 1].set_title('数据质量分布')
            
            # 3. 数值分布直方图
            try:
                df = self.connector.query_to_dataframe("""
                    SELECT value FROM engineering_data 
                    WHERE quality = 192 
                    LIMIT 10000
                """)
                
                if not df.empty:
                    axes[1, 0].hist(df['value'], bins=50, alpha=0.7)
                    axes[1, 0].set_title('数值分布直方图')
                    axes[1, 0].set_xlabel('数值')
                    axes[1, 0].set_ylabel('频次')
            except:
                axes[1, 0].text(0.5, 0.5, '无法获取数值分布', ha='center', va='center')
                axes[1, 0].set_title('数值分布直方图')
            
            # 4. 文件大小信息
            file_info = ['Database', 'WAL', 'Total']
            sizes = [
                db_info.get('database_size_mb', 0),
                db_info.get('wal_size_mb', 0),
                db_info.get('total_size_mb', 0)
            ]
            
            axes[1, 1].bar(file_info, sizes)
            axes[1, 1].set_title('文件大小 (MB)')
            axes[1, 1].set_ylabel('大小 (MB)')
            
            plt.tight_layout()
            plt.savefig(output_path / 'basic_statistics.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            # 保存统计数据到JSON
            report_data = {
                'database_info': db_info,
                'statistics': stats,
                'generated_at': datetime.now().isoformat()
            }
            
            with open(output_path / 'basic_statistics.json', 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            print("   ✅ 基础统计报告已生成")
            
        except Exception as e:
            print(f"   ❌ 生成基础统计报告失败: {e}")
    
    def _generate_time_series_analysis(self, output_path: Path) -> None:
        """生成时间序列分析"""
        print("⏰ 生成时间序列分析...")
        
        try:
            # 获取时间序列数据
            df = self.connector.query_to_dataframe("""
                SELECT 
                    strftime('%Y-%m-%d %H:00:00', datetime(timestamp, 'unixepoch')) as hour,
                    COUNT(*) as record_count,
                    AVG(value) as avg_value,
                    AVG(CASE WHEN quality = 192 THEN 1.0 ELSE 0.0 END) * 100 as good_quality_pct
                FROM engineering_data 
                GROUP BY hour
                ORDER BY hour
                LIMIT 168  -- 最近一周的小时数据
            """)
            
            if df.empty:
                print("   ❌ 无时间序列数据")
                return
            
            # 转换时间格式
            df['hour'] = pd.to_datetime(df['hour'])
            
            # 创建时间序列图表
            fig, axes = plt.subplots(3, 1, figsize=(15, 12))
            fig.suptitle('时间序列分析报告', fontsize=16, fontweight='bold')
            
            # 1. 记录数时间序列
            axes[0].plot(df['hour'], df['record_count'], marker='o', linewidth=2)
            axes[0].set_title('每小时记录数趋势')
            axes[0].set_ylabel('记录数')
            axes[0].grid(True, alpha=0.3)
            
            # 2. 平均值时间序列
            axes[1].plot(df['hour'], df['avg_value'], marker='s', color='orange', linewidth=2)
            axes[1].set_title('每小时平均值趋势')
            axes[1].set_ylabel('平均值')
            axes[1].grid(True, alpha=0.3)
            
            # 3. 数据质量时间序列
            axes[2].plot(df['hour'], df['good_quality_pct'], marker='^', color='green', linewidth=2)
            axes[2].set_title('每小时数据质量趋势')
            axes[2].set_ylabel('好质量百分比 (%)')
            axes[2].set_xlabel('时间')
            axes[2].grid(True, alpha=0.3)
            
            # 格式化x轴
            for ax in axes:
                ax.tick_params(axis='x', rotation=45)
            
            plt.tight_layout()
            plt.savefig(output_path / 'time_series_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            # 保存时间序列数据
            df.to_csv(output_path / 'time_series_data.csv', index=False)
            
            print("   ✅ 时间序列分析已生成")
            
        except Exception as e:
            print(f"   ❌ 生成时间序列分析失败: {e}")
    
    def _generate_device_performance_analysis(self, output_path: Path) -> None:
        """生成设备性能分析"""
        print("🖥️ 生成设备性能分析...")
        
        try:
            # 获取设备性能数据
            df = self.connector.query_to_dataframe("""
                SELECT 
                    device_id,
                    COUNT(*) as total_records,
                    AVG(value) as avg_value,
                    MIN(value) as min_value,
                    MAX(value) as max_value,
                    AVG(CASE WHEN quality = 192 THEN 1.0 ELSE 0.0 END) * 100 as good_quality_pct,
                    COUNT(CASE WHEN quality != 192 THEN 1 END) as bad_records
                FROM engineering_data 
                GROUP BY device_id
                ORDER BY total_records DESC
            """)
            
            if df.empty:
                print("   ❌ 无设备性能数据")
                return
            
            # 创建设备性能图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('设备性能分析报告', fontsize=16, fontweight='bold')
            
            # 1. 设备记录数对比
            axes[0, 0].barh(df['device_id'], df['total_records'])
            axes[0, 0].set_title('设备记录数对比')
            axes[0, 0].set_xlabel('记录数')
            
            # 2. 设备数据质量对比
            axes[0, 1].barh(df['device_id'], df['good_quality_pct'])
            axes[0, 1].set_title('设备数据质量对比')
            axes[0, 1].set_xlabel('好质量百分比 (%)')
            
            # 3. 设备平均值对比
            axes[1, 0].barh(df['device_id'], df['avg_value'])
            axes[1, 0].set_title('设备平均值对比')
            axes[1, 0].set_xlabel('平均值')
            
            # 4. 设备异常记录数
            axes[1, 1].barh(df['device_id'], df['bad_records'])
            axes[1, 1].set_title('设备异常记录数')
            axes[1, 1].set_xlabel('异常记录数')
            
            plt.tight_layout()
            plt.savefig(output_path / 'device_performance.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            # 保存设备性能数据
            df.to_csv(output_path / 'device_performance.csv', index=False)
            
            print("   ✅ 设备性能分析已生成")
            
        except Exception as e:
            print(f"   ❌ 生成设备性能分析失败: {e}")
    
    def _generate_point_type_analysis(self, output_path: Path) -> None:
        """生成测点类型分析"""
        print("🔧 生成测点类型分析...")
        
        try:
            # 获取测点类型数据
            df = self.connector.query_to_dataframe("""
                SELECT 
                    CASE 
                        WHEN point_id LIKE '%TI%' THEN 'TI (Temperature)'
                        WHEN point_id LIKE '%PI%' THEN 'PI (Pressure)'
                        WHEN point_id LIKE '%FI%' THEN 'FI (Flow)'
                        WHEN point_id LIKE '%LI%' THEN 'LI (Level)'
                        WHEN point_id LIKE '%AI%' THEN 'AI (Analog)'
                        WHEN point_id LIKE '%VI%' THEN 'VI (Vibration)'
                        WHEN point_id LIKE '%SI%' THEN 'SI (Speed)'
                        ELSE 'Other'
                    END as point_type,
                    COUNT(*) as record_count,
                    AVG(value) as avg_value,
                    MIN(value) as min_value,
                    MAX(value) as max_value,
                    COUNT(DISTINCT point_id) as unique_points
                FROM engineering_data 
                GROUP BY point_type
                ORDER BY record_count DESC
            """)
            
            if df.empty:
                print("   ❌ 无测点类型数据")
                return
            
            # 创建测点类型分析图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('测点类型分析报告', fontsize=16, fontweight='bold')
            
            # 1. 测点类型记录数分布
            axes[0, 0].pie(df['record_count'], labels=df['point_type'], autopct='%1.1f%%')
            axes[0, 0].set_title('测点类型记录数分布')
            
            # 2. 测点类型平均值对比
            axes[0, 1].bar(range(len(df)), df['avg_value'])
            axes[0, 1].set_title('测点类型平均值对比')
            axes[0, 1].set_xlabel('测点类型')
            axes[0, 1].set_ylabel('平均值')
            axes[0, 1].set_xticks(range(len(df)))
            axes[0, 1].set_xticklabels(df['point_type'], rotation=45)
            
            # 3. 测点类型数值范围
            x_pos = range(len(df))
            axes[1, 0].bar(x_pos, df['max_value'] - df['min_value'], bottom=df['min_value'])
            axes[1, 0].set_title('测点类型数值范围')
            axes[1, 0].set_xlabel('测点类型')
            axes[1, 0].set_ylabel('数值')
            axes[1, 0].set_xticks(x_pos)
            axes[1, 0].set_xticklabels(df['point_type'], rotation=45)
            
            # 4. 唯一测点数量
            axes[1, 1].bar(range(len(df)), df['unique_points'])
            axes[1, 1].set_title('测点类型唯一测点数量')
            axes[1, 1].set_xlabel('测点类型')
            axes[1, 1].set_ylabel('唯一测点数')
            axes[1, 1].set_xticks(range(len(df)))
            axes[1, 1].set_xticklabels(df['point_type'], rotation=45)
            
            plt.tight_layout()
            plt.savefig(output_path / 'point_type_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            # 保存测点类型数据
            df.to_csv(output_path / 'point_type_analysis.csv', index=False)
            
            print("   ✅ 测点类型分析已生成")
            
        except Exception as e:
            print(f"   ❌ 生成测点类型分析失败: {e}")
    
    def _generate_data_quality_report(self, output_path: Path) -> None:
        """生成数据质量报告"""
        print("✅ 生成数据质量报告...")
        
        try:
            # 获取数据质量统计
            quality_stats = self.connector.query("""
                SELECT 
                    quality,
                    COUNT(*) as count,
                    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM engineering_data) as percentage
                FROM engineering_data 
                GROUP BY quality 
                ORDER BY quality
            """)
            
            # 创建质量报告
            quality_report = {
                'total_records': sum(row['count'] for row in quality_stats),
                'quality_breakdown': quality_stats,
                'quality_summary': {
                    'good_quality_pct': sum(row['percentage'] for row in quality_stats if row['quality'] == 192),
                    'bad_quality_pct': sum(row['percentage'] for row in quality_stats if row['quality'] != 192)
                }
            }
            
            # 保存质量报告
            with open(output_path / 'data_quality_report.json', 'w', encoding='utf-8') as f:
                json.dump(quality_report, f, indent=2, ensure_ascii=False)
            
            print("   ✅ 数据质量报告已生成")
            
        except Exception as e:
            print(f"   ❌ 生成数据质量报告失败: {e}")
    
    def _generate_anomaly_detection_report(self, output_path: Path) -> None:
        """生成异常检测报告"""
        print("🚨 生成异常检测报告...")
        
        try:
            # 检测异常值（使用3σ原则）
            anomalies = self.connector.query("""
                WITH stats AS (
                    SELECT 
                        AVG(value) as mean_val,
                        (MAX(value) - MIN(value)) / 4.0 as std_estimate
                    FROM engineering_data 
                    WHERE quality = 192
                )
                SELECT 
                    point_id,
                    timestamp,
                    value,
                    quality,
                    device_id,
                    ABS(value - stats.mean_val) as deviation
                FROM engineering_data, stats
                WHERE ABS(value - stats.mean_val) > 3 * stats.std_estimate
                ORDER BY deviation DESC
                LIMIT 100
            """)
            
            anomaly_report = {
                'anomaly_count': len(anomalies),
                'anomalies': anomalies,
                'detection_method': '3-sigma rule',
                'generated_at': datetime.now().isoformat()
            }
            
            # 保存异常检测报告
            with open(output_path / 'anomaly_detection.json', 'w', encoding='utf-8') as f:
                json.dump(anomaly_report, f, indent=2, ensure_ascii=False)
            
            print(f"   ✅ 异常检测报告已生成，发现 {len(anomalies)} 个异常值")
            
        except Exception as e:
            print(f"   ❌ 生成异常检测报告失败: {e}")
    
    def close(self) -> None:
        """关闭连接"""
        self.connector.close()

def main():
    """主函数"""
    print("📊 nclink_v2.duckdb 高级数据分析工具")
    print("=" * 50)
    
    # 检查数据库文件
    db_files = ["nclink_v2.duckdb", "engineering_data.db"]
    db_file = None
    
    for file in db_files:
        if Path(file).exists():
            db_file = file
            break
    
    if not db_file:
        print("❌ 未找到数据库文件")
        return
    
    print(f"📁 分析数据库: {db_file}")
    
    try:
        # 创建分析器
        analyzer = NclinkV2Analyzer(db_file)
        
        if not analyzer.connect():
            print("❌ 连接数据库失败")
            return
        
        # 生成综合报告
        analyzer.generate_comprehensive_report()
        
        analyzer.close()
        
        print("\n🎉 分析完成!")
        print("💡 查看生成的报告文件:")
        print("   - analysis_reports/basic_statistics.png")
        print("   - analysis_reports/time_series_analysis.png")
        print("   - analysis_reports/device_performance.png")
        print("   - analysis_reports/point_type_analysis.png")
        print("   - analysis_reports/*.json (详细数据)")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
