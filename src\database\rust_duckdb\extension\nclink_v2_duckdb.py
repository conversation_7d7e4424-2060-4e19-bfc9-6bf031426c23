#!/usr/bin/env python3
"""
nclink_v2数据库DuckDB原生操作脚本
使用DuckDB Python库进行高性能数据操作
"""

import os
import sys
import json
import struct
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import pandas as pd
import numpy as np
import duckdb

class NclinkV2DuckDB:
    """nclink_v2数据库DuckDB原生操作类"""
    
    def __init__(self, db_path: str = None):
        """
        初始化DuckDB连接
        
        Args:
            db_path: 数据库文件路径，None表示内存数据库
        """
        # DuckDB已直接导入，无需检查
        
        self.db_path = Path(db_path) if db_path else None
        self.binary_path = Path(db_path.replace('.duckdb', '_binary.duckdb')) if db_path else None
        
        # 创建DuckDB连接
        if db_path and Path(db_path).exists() and Path(db_path).suffix == '.duckdb':
            # 如果是标准DuckDB文件，直接连接
            self.conn = duckdb.connect(db_path)
            print(f"✅ 连接到DuckDB文件: {Path(db_path).name}")
        else:
            # 创建内存数据库
            self.conn = duckdb.connect(':memory:')
            print("✅ 创建内存DuckDB数据库")
            
            # 如果提供了二进制文件路径，加载数据
            if db_path and Path(db_path).exists():
                self._load_from_binary(db_path)
        
        # 创建表结构
        self._create_tables()
    
    def _create_tables(self):
        """创建DuckDB表结构"""
        create_sql = """
        CREATE TABLE IF NOT EXISTS nclink_data (
            id BIGINT PRIMARY KEY,
            timestamp BIGINT NOT NULL,
            point_id VARCHAR NOT NULL,
            value DOUBLE NOT NULL,
            quality INTEGER NOT NULL,
            device_id VARCHAR NOT NULL,
            data_type VARCHAR NOT NULL,
            unit VARCHAR NOT NULL,
            plant_id INTEGER NOT NULL,
            tags VARCHAR NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建索引提高查询性能
        CREATE INDEX IF NOT EXISTS idx_timestamp ON nclink_data(timestamp);
        CREATE INDEX IF NOT EXISTS idx_point_id ON nclink_data(point_id);
        CREATE INDEX IF NOT EXISTS idx_device_id ON nclink_data(device_id);
        CREATE INDEX IF NOT EXISTS idx_data_type ON nclink_data(data_type);
        CREATE INDEX IF NOT EXISTS idx_plant_id ON nclink_data(plant_id);
        CREATE INDEX IF NOT EXISTS idx_value ON nclink_data(value);
        """
        
        self.conn.execute(create_sql)
        print("✅ 数据表结构创建完成")
    
    def _load_from_binary(self, binary_path: str):
        """从二进制文件加载数据"""
        print(f"📥 从二进制文件加载数据: {Path(binary_path).name}")
        
        try:
            with open(binary_path, 'rb') as f:
                # 读取文件头
                magic = f.read(5)
                if magic != b'NLKDB':
                    raise ValueError(f"无效的数据库文件格式: {magic}")
                
                version = struct.unpack('B', f.read(1))[0]
                endian_flag = struct.unpack('B', f.read(1))[0]
                record_count = struct.unpack('<Q', f.read(8))[0]

                print(f"📋 文件信息: 版本={version}, 字节序={endian_flag}, 记录数={record_count:,}")
                
                # 批量读取记录
                batch_size = 10000
                total_loaded = 0
                
                for batch_start in range(0, record_count, batch_size):
                    batch_end = min(batch_start + batch_size, record_count)
                    batch_records = []
                    
                    for i in range(batch_start, batch_end):
                        try:
                            record = self._read_binary_record(f)
                            batch_records.append(record)
                        except Exception as e:
                            print(f"⚠️ 读取记录 {i} 失败: {e}")
                            break
                    
                    if batch_records:
                        self._batch_insert_duckdb(batch_records)
                        total_loaded += len(batch_records)
                        
                        if total_loaded % 50000 == 0:
                            print(f"   已加载: {total_loaded:,} 条记录")
                
                print(f"✅ 成功加载 {total_loaded:,} 条记录")
                
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            raise
    
    def _read_binary_record(self, f) -> Dict[str, Any]:
        """读取单条二进制记录"""
        # 读取固定字段
        id_val = struct.unpack('<Q', f.read(8))[0]
        timestamp = struct.unpack('<Q', f.read(8))[0]
        value = struct.unpack('<d', f.read(8))[0]
        quality = struct.unpack('<I', f.read(4))[0]
        plant_id = struct.unpack('<I', f.read(4))[0]
        
        # 读取字符串字段
        point_id = self._read_string(f)
        device_id = self._read_string(f)
        data_type = self._read_string(f)
        unit = self._read_string(f)
        tags = self._read_string(f)
        
        return {
            'id': id_val,
            'timestamp': timestamp,
            'point_id': point_id,
            'value': value,
            'quality': quality,
            'device_id': device_id,
            'data_type': data_type,
            'unit': unit,
            'plant_id': plant_id,
            'tags': tags
        }
    
    def _read_string(self, f) -> str:
        """读取字符串"""
        length = struct.unpack('<I', f.read(4))[0]
        if length > 10000:  # 安全检查
            raise ValueError(f"字符串长度异常: {length}")
        return f.read(length).decode('utf-8')
    
    def _batch_insert_duckdb(self, records: List[Dict[str, Any]]):
        """批量插入到DuckDB"""
        if not records:
            return
        
        # 准备数据
        data = [
            (r['id'], r['timestamp'], r['point_id'], r['value'], r['quality'],
             r['device_id'], r['data_type'], r['unit'], r['plant_id'], r['tags'])
            for r in records
        ]
        
        # 使用DuckDB的高效批量插入
        insert_sql = """
        INSERT INTO nclink_data 
        (id, timestamp, point_id, value, quality, device_id, data_type, unit, plant_id, tags)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        self.conn.executemany(insert_sql, data)
    
    # ==================== DuckDB原生CRUD操作 ====================
    
    def create_record(self, point_id: str, value: float, quality: int = 192,
                     device_id: str = "PLC_01", data_type: str = "AI", 
                     unit: str = "", plant_id: int = 1, tags: Dict = None) -> int:
        """创建新记录"""
        if tags is None:
            tags = {}
        
        # 生成新ID
        result = self.conn.execute("SELECT MAX(id) FROM nclink_data").fetchone()
        max_id = result[0] if result[0] is not None else 0
        new_id = max_id + 1
        
        # 当前时间戳
        timestamp = int(datetime.now().timestamp() * 1_000_000_000)
        
        # 插入记录
        self.conn.execute("""
            INSERT INTO nclink_data 
            (id, timestamp, point_id, value, quality, device_id, data_type, unit, plant_id, tags)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (new_id, timestamp, point_id, value, quality,
              device_id, data_type, unit, plant_id, json.dumps(tags)))
        
        print(f"✅ 创建记录成功: ID={new_id}, Point={point_id}, Value={value}")
        return new_id
    
    def read_records(self, limit: int = 100, offset: int = 0, 
                    filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """读取记录"""
        sql = "SELECT * FROM nclink_data"
        params = []
        
        # 构建WHERE条件
        if filters:
            conditions = []
            for key, value in filters.items():
                if key in ['id', 'plant_id', 'quality']:
                    conditions.append(f"{key} = ?")
                    params.append(value)
                elif key in ['point_id', 'device_id', 'data_type', 'unit']:
                    conditions.append(f"{key} LIKE ?")
                    params.append(f"%{value}%")
                elif key == 'value_range':
                    conditions.append("value BETWEEN ? AND ?")
                    params.extend(value)
                elif key == 'timestamp_range':
                    conditions.append("timestamp BETWEEN ? AND ?")
                    params.extend(value)
            
            if conditions:
                sql += " WHERE " + " AND ".join(conditions)
        
        sql += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        # 执行查询
        result = self.conn.execute(sql, params).fetchall()
        columns = [desc[0] for desc in self.conn.description]
        
        # 转换为字典列表
        records = []
        for row in result:
            record = dict(zip(columns, row))
            # 添加可读时间
            record['datetime'] = datetime.fromtimestamp(record['timestamp'] / 1_000_000_000)
            try:
                record['tags_dict'] = json.loads(record['tags'])
            except:
                record['tags_dict'] = {}
            records.append(record)
        
        return records
    
    def update_record(self, record_id: int, updates: Dict[str, Any]) -> bool:
        """更新记录"""
        if not updates:
            return False
        
        # 构建UPDATE语句
        set_clauses = []
        params = []
        
        for key, value in updates.items():
            if key in ['point_id', 'value', 'quality', 'device_id', 'data_type', 
                      'unit', 'plant_id', 'tags']:
                set_clauses.append(f"{key} = ?")
                params.append(value if key != 'tags' else json.dumps(value))
        
        if not set_clauses:
            return False
        
        # 添加更新时间
        set_clauses.append("updated_at = CURRENT_TIMESTAMP")
        params.append(record_id)
        
        sql = f"UPDATE nclink_data SET {', '.join(set_clauses)} WHERE id = ?"
        
        result = self.conn.execute(sql, params)
        success = result.rowcount > 0
        
        if success:
            print(f"✅ 更新记录成功: ID={record_id}")
        else:
            print(f"⚠️ 记录不存在: ID={record_id}")
        
        return success
    
    def delete_record(self, record_id: int) -> bool:
        """删除记录"""
        result = self.conn.execute("DELETE FROM nclink_data WHERE id = ?", (record_id,))
        success = result.rowcount > 0
        
        if success:
            print(f"✅ 删除记录成功: ID={record_id}")
        else:
            print(f"⚠️ 记录不存在: ID={record_id}")
        
        return success
    
    def delete_records(self, filters: Dict[str, Any]) -> int:
        """批量删除记录"""
        if not filters:
            print("⚠️ 批量删除需要指定过滤条件")
            return 0
        
        # 构建WHERE条件
        conditions = []
        params = []
        
        for key, value in filters.items():
            if key in ['plant_id', 'quality']:
                conditions.append(f"{key} = ?")
                params.append(value)
            elif key in ['device_id', 'data_type']:
                conditions.append(f"{key} LIKE ?")
                params.append(f"%{value}%")
        
        if not conditions:
            print("⚠️ 无效的删除条件")
            return 0
        
        sql = f"DELETE FROM nclink_data WHERE {' AND '.join(conditions)}"
        result = self.conn.execute(sql, params)
        
        deleted_count = result.rowcount
        print(f"✅ 批量删除成功: {deleted_count} 条记录")
        return deleted_count
    
    # ==================== DuckDB高级分析功能 ====================
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        stats = {}
        
        # 基本统计
        result = self.conn.execute("SELECT COUNT(*) FROM nclink_data").fetchone()
        stats['total_records'] = result[0]
        
        result = self.conn.execute("SELECT COUNT(DISTINCT point_id) FROM nclink_data").fetchone()
        stats['unique_points'] = result[0]
        
        result = self.conn.execute("SELECT COUNT(DISTINCT device_id) FROM nclink_data").fetchone()
        stats['unique_devices'] = result[0]
        
        result = self.conn.execute("SELECT COUNT(DISTINCT plant_id) FROM nclink_data").fetchone()
        stats['unique_plants'] = result[0]
        
        # 数据类型分布
        result = self.conn.execute("""
            SELECT data_type, COUNT(*) as count 
            FROM nclink_data 
            GROUP BY data_type 
            ORDER BY count DESC
        """).fetchall()
        stats['data_type_distribution'] = dict(result)
        
        # 数据质量统计
        result = self.conn.execute("""
            SELECT 
                SUM(CASE WHEN quality = 192 THEN 1 ELSE 0 END) as good_quality,
                SUM(CASE WHEN quality != 192 THEN 1 ELSE 0 END) as bad_quality
            FROM nclink_data
        """).fetchone()
        
        stats['quality_stats'] = {
            'good': result[0],
            'bad': result[1],
            'good_percentage': result[0] / stats['total_records'] * 100 if stats['total_records'] > 0 else 0
        }
        
        # 数值统计
        result = self.conn.execute("""
            SELECT 
                MIN(value) as min_value,
                MAX(value) as max_value,
                AVG(value) as avg_value,
                STDDEV(value) as std_value,
                COUNT(*) as count
            FROM nclink_data
        """).fetchone()
        
        stats['value_stats'] = {
            'min': result[0],
            'max': result[1],
            'avg': result[2],
            'std': result[3],
            'count': result[4]
        }
        
        return stats
    
    def get_dataframe(self, sql: str = None, filters: Dict[str, Any] = None, 
                     limit: int = 10000) -> pd.DataFrame:
        """获取Pandas DataFrame"""
        if sql:
            # 执行自定义SQL
            return self.conn.execute(sql).df()
        else:
            # 使用过滤条件
            records = self.read_records(limit=limit, filters=filters)
            if not records:
                return pd.DataFrame()
            
            df = pd.DataFrame(records)
            # 转换时间戳
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ns')
            return df
    
    def execute_sql(self, sql: str, params: tuple = None) -> Any:
        """执行自定义SQL"""
        if params:
            return self.conn.execute(sql, params)
        else:
            return self.conn.execute(sql)
    
    def save_to_file(self, file_path: str):
        """保存数据库到文件"""
        if file_path.endswith('.parquet'):
            # 导出为Parquet格式
            self.conn.execute(f"COPY nclink_data TO '{file_path}' (FORMAT PARQUET)")
        elif file_path.endswith('.csv'):
            # 导出为CSV格式
            self.conn.execute(f"COPY nclink_data TO '{file_path}' (FORMAT CSV, HEADER)")
        else:
            # 保存为DuckDB文件
            self.conn.execute(f"EXPORT DATABASE '{file_path}'")
        
        print(f"✅ 数据库已保存到: {file_path}")
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("✅ DuckDB连接已关闭")

def main():
    """主函数 - DuckDB版本演示"""
    print("🦆 nclink_v2数据库DuckDB原生操作演示")
    print("=" * 60)
    
    # 连接数据库 - 使用标准DuckDB数据库
    db_path = r"D:\database\nclink_v2_standard.duckdb"
    
    try:
        db = NclinkV2DuckDB(db_path)
        
        print("\n📊 数据库统计信息:")
        stats = db.get_statistics()
        print(f"   总记录数: {stats['total_records']:,}")
        print(f"   测点数: {stats['unique_points']:,}")
        print(f"   设备数: {stats['unique_devices']}")
        print(f"   工厂数: {stats['unique_plants']}")
        print(f"   数据质量: {stats['quality_stats']['good_percentage']:.1f}% 良好")
        
        print("\n📈 数据类型分布:")
        for data_type, count in stats['data_type_distribution'].items():
            print(f"   {data_type}: {count:,} 条")
        
        print("\n🧪 DuckDB高级功能演示:")
        
        # 1. 高性能聚合查询
        print("\n1️⃣ 高性能聚合查询:")
        agg_df = db.get_dataframe(sql="""
            SELECT 
                data_type,
                device_id,
                COUNT(*) as record_count,
                AVG(value) as avg_value,
                STDDEV(value) as std_value,
                MIN(value) as min_value,
                MAX(value) as max_value
            FROM nclink_data 
            GROUP BY data_type, device_id
            ORDER BY data_type, device_id
            LIMIT 10
        """)
        print(agg_df)
        
        # 2. 时间序列分析
        print("\n2️⃣ 时间序列分析:")
        ts_df = db.get_dataframe(sql="""
            SELECT 
                data_type,
                DATE_TRUNC('hour', to_timestamp(timestamp / 1000000000)) as hour,
                AVG(value) as avg_value,
                COUNT(*) as count
            FROM nclink_data 
            WHERE data_type = 'TI'
            GROUP BY data_type, hour
            ORDER BY hour
            LIMIT 5
        """)
        print(ts_df)
        
        # 3. 窗口函数分析
        print("\n3️⃣ 窗口函数分析:")
        window_df = db.get_dataframe(sql="""
            SELECT 
                point_id,
                value,
                LAG(value) OVER (PARTITION BY point_id ORDER BY timestamp) as prev_value,
                value - LAG(value) OVER (PARTITION BY point_id ORDER BY timestamp) as value_diff
            FROM nclink_data 
            WHERE data_type = 'TI'
            ORDER BY point_id, timestamp
            LIMIT 10
        """)
        print(window_df)
        
        # 4. 导出数据
        print("\n4️⃣ 数据导出:")
        db.save_to_file("nclink_v2_export.parquet")
        db.save_to_file("nclink_v2_export.csv")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
