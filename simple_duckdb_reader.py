#!/usr/bin/env python3
"""
简单的DuckDB文件读取器
直接读取nclink_v2.duckdb和nclink_v2.duckdb.wal文件
"""

import sqlite3
import struct
import json
from datetime import datetime
from pathlib import Path

class SimpleDuckDBReader:
    """简单的DuckDB文件读取器"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.connection = None
    
    def connect(self):
        """连接数据库"""
        try:
            # 直接使用SQLite连接（兼容模式）
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def read_header(self):
        """读取DuckDB文件头信息"""
        try:
            with open(self.db_path, 'rb') as f:
                magic = f.read(8)
                version = struct.unpack('<I', f.read(4))[0]
                page_size = struct.unpack('<I', f.read(4))[0]
                compression_type = struct.unpack('<I', f.read(4))[0]
                created_at = struct.unpack('<Q', f.read(8))[0]
                modified_at = struct.unpack('<Q', f.read(8))[0]
                record_count = struct.unpack('<Q', f.read(8))[0]
                compression_ratio = struct.unpack('<d', f.read(8))[0]
                
                return {
                    'version': version,
                    'page_size': page_size,
                    'compression_type': ['None', 'LZ4', 'Gzip', 'Snappy', 'ZSTD'][compression_type],
                    'created_at': datetime.fromtimestamp(created_at).isoformat(),
                    'record_count': record_count,
                    'compression_ratio': compression_ratio
                }
        except:
            return None
    
    def query(self, sql, params=None):
        """执行SQL查询"""
        if not self.connection:
            raise RuntimeError("未连接数据库")
        
        cursor = self.connection.cursor()
        if params:
            cursor.execute(sql, params)
        else:
            cursor.execute(sql)
        
        return [dict(row) for row in cursor.fetchall()]
    
    def get_all_data(self, limit=1000):
        """获取所有数据"""
        return self.query(f"SELECT * FROM engineering_data LIMIT {limit}")
    
    def get_by_device(self, device_id, limit=100):
        """按设备查询"""
        return self.query(
            "SELECT * FROM engineering_data WHERE device_id = ? ORDER BY timestamp DESC LIMIT ?",
            (device_id, limit)
        )
    
    def get_by_point_type(self, point_type, limit=100):
        """按测点类型查询"""
        return self.query(
            f"SELECT * FROM engineering_data WHERE point_id LIKE '%{point_type}%' ORDER BY timestamp DESC LIMIT ?",
            (limit,)
        )
    
    def get_statistics(self):
        """获取统计信息"""
        stats = self.query("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT point_id) as unique_points,
                COUNT(DISTINCT device_id) as unique_devices,
                MIN(timestamp) as start_time,
                MAX(timestamp) as end_time,
                AVG(value) as avg_value
            FROM engineering_data
        """)[0]
        
        return stats
    
    def close(self):
        """关闭连接"""
        if self.connection:
            self.connection.close()

# 使用示例
if __name__ == "__main__":
    # 1. 连接数据库
    db = SimpleDuckDBReader("engineering_data.db")  # 或 "nclink_v2.duckdb"
    
    if db.connect():
        print("✅ 连接成功")
        
        # 2. 读取文件头信息
        header = db.read_header()
        if header:
            print(f"📊 记录数: {header['record_count']:,}")
            print(f"🗜️ 压缩比: {header['compression_ratio']:.1f}:1")
        
        # 3. 获取统计信息
        stats = db.get_statistics()
        print(f"📈 总记录: {stats['total_records']:,}")
        print(f"🔧 测点数: {stats['unique_points']:,}")
        print(f"🖥️ 设备数: {stats['unique_devices']:,}")
        
        # 4. 查询数据
        recent_data = db.get_all_data(5)
        print("\n📋 最新5条记录:")
        for record in recent_data:
            dt = datetime.fromtimestamp(record['timestamp'])
            print(f"  {record['point_id']}: {record['value']:.2f} @ {dt}")
        
        # 5. 按设备查询
        device_data = db.get_by_device("Plant_A_Unit_01_Reactor_PLC", 3)
        print(f"\n🖥️ 设备数据 ({len(device_data)} 条):")
        for record in device_data:
            print(f"  {record['point_id']}: {record['value']:.2f}")
        
        db.close()
    else:
        print("❌ 连接失败")
