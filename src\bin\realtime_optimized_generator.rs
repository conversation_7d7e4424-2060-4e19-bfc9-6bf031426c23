/// 实时数据写入优化生成器
/// 专为1毫秒1条的高频写入场景设计

use std::fs::{File, create_dir_all};
use std::io::{Write, BufWriter};
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH, Instant};
use std::sync::{Arc, Mutex, mpsc};
use std::thread;
use byteorder::{LittleEndian, WriteBytesExt};
use serde_json::json;
use rand::Rng;

/// 实时写入策略
#[derive(Debug, <PERSON><PERSON>, Copy)]
enum RealtimeStrategy {
    /// 无压缩直写 - 最快
    NoCompression,
    /// LZ4异步压缩 - 平衡
    LZ4Async,
    /// 内存缓冲 + 批量压缩
    BufferedBatch,
}

/// 工程数据记录
#[derive(Debug, <PERSON><PERSON>)]
struct RealtimeRecord {
    id: u64,
    point_id: String,
    timestamp: u64,
    value: f64,
    quality: u32,
    device_id: String,
    tags: String,
}

/// 实时写入统计
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
struct RealtimeStats {
    total_records: u64,
    write_time_ns: u64,
    max_latency_ns: u64,
    min_latency_ns: u64,
    avg_latency_ns: u64,
    throughput_per_sec: f64,
}

/// 实时优化生成器
struct RealtimeOptimizedGenerator {
    output_dir: String,
    strategy: RealtimeStrategy,
    buffer_size: usize,
    stats: Arc<Mutex<RealtimeStats>>,
}

impl RealtimeOptimizedGenerator {
    /// 创建实时优化生成器
    fn new(output_dir: String, strategy: RealtimeStrategy, buffer_size: usize) -> Self {
        Self {
            output_dir,
            strategy,
            buffer_size,
            stats: Arc::new(Mutex::new(RealtimeStats::default())),
        }
    }
    
    /// 生成实时数据记录
    fn generate_realtime_record(&self, index: usize) -> RealtimeRecord {
        let mut rng = rand::thread_rng();
        
        // 简化的数据生成，减少CPU开销
        let plant_id = (index % 3) + 1;
        let unit_id = (index % 4) + 1;
        let point_type = match index % 4 {
            0 => "TI",
            1 => "PI", 
            2 => "FI",
            _ => "LI",
        };
        
        let point_id = format!("P{}_U{}_{:03}", plant_id, unit_id, index % 100);
        
        // 高精度时间戳
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_nanos() as u64;
        
        // 简化的数值生成
        let base_value = match point_type {
            "TI" => 25.0,
            "PI" => 101.3,
            "FI" => 50.0,
            _ => 2.5,
        };
        let value = base_value + (rng.gen::<f64>() - 0.5) * 10.0;
        
        let quality = if rng.gen_bool(0.98) { 192 } else { 0 };
        let device_id = format!("PLC_{}", plant_id);
        
        // 最小化JSON生成
        let tags = json!({
            "type": point_type,
            "plant": plant_id
        }).to_string();
        
        RealtimeRecord {
            id: index as u64 + 1,
            point_id,
            timestamp,
            value,
            quality,
            device_id,
            tags,
        }
    }
    
    /// 序列化记录为字节数组（最优化）
    fn serialize_record_fast(&self, record: &RealtimeRecord) -> Vec<u8> {
        let mut data = Vec::with_capacity(256); // 预分配容量
        
        // 使用二进制格式，避免JSON开销
        data.extend_from_slice(&record.id.to_le_bytes());
        data.extend_from_slice(&record.timestamp.to_le_bytes());
        data.extend_from_slice(&record.value.to_le_bytes());
        data.extend_from_slice(&record.quality.to_le_bytes());
        
        // 字符串长度 + 内容
        let point_id_bytes = record.point_id.as_bytes();
        data.extend_from_slice(&(point_id_bytes.len() as u32).to_le_bytes());
        data.extend_from_slice(point_id_bytes);
        
        let device_id_bytes = record.device_id.as_bytes();
        data.extend_from_slice(&(device_id_bytes.len() as u32).to_le_bytes());
        data.extend_from_slice(device_id_bytes);
        
        let tags_bytes = record.tags.as_bytes();
        data.extend_from_slice(&(tags_bytes.len() as u32).to_le_bytes());
        data.extend_from_slice(tags_bytes);
        
        data
    }
    
    /// 无压缩直写策略
    fn strategy_no_compression(&self, record_count: usize) -> Result<(), String> {
        println!("🚀 使用无压缩直写策略 (最低延迟)");
        
        let db_path = Path::new(&self.output_dir).join("realtime_v2.duckdb");
        let wal_path = Path::new(&self.output_dir).join("realtime_v2.duckdb.wal");
        
        // 创建高性能写入器
        let mut db_writer = BufWriter::with_capacity(64 * 1024, 
            File::create(&db_path).map_err(|e| format!("创建数据库文件失败: {}", e))?);
        
        let mut wal_writer = BufWriter::with_capacity(16 * 1024,
            File::create(&wal_path).map_err(|e| format!("创建WAL文件失败: {}", e))?);
        
        let start_time = Instant::now();
        let mut latencies = Vec::with_capacity(record_count);
        
        println!("⚡ 开始实时写入测试...");
        
        for i in 0..record_count {
            let record_start = Instant::now();
            
            // 生成记录
            let record = self.generate_realtime_record(i);
            
            // 序列化
            let data = self.serialize_record_fast(&record);
            
            // 写入数据文件
            db_writer.write_all(&data)
                .map_err(|e| format!("写入数据失败: {}", e))?;
            
            // 写入WAL
            wal_writer.write_u64::<LittleEndian>(record.timestamp)
                .map_err(|e| format!("写入WAL失败: {}", e))?;
            wal_writer.write_u64::<LittleEndian>(record.id)
                .map_err(|e| format!("写入WAL失败: {}", e))?;
            
            let latency = record_start.elapsed().as_nanos() as u64;
            latencies.push(latency);
            
            // 每1000条记录刷新一次
            if i % 1000 == 0 {
                db_writer.flush().map_err(|e| format!("刷新失败: {}", e))?;
                wal_writer.flush().map_err(|e| format!("刷新WAL失败: {}", e))?;
                
                if i > 0 {
                    let avg_latency = latencies.iter().sum::<u64>() / latencies.len() as u64;
                    println!("📊 已写入: {} 条 | 平均延迟: {:.2} μs", i + 1, avg_latency as f64 / 1000.0);
                }
            }
            
            // 模拟1毫秒间隔
            if i < record_count - 1 {
                std::thread::sleep(std::time::Duration::from_micros(1000));
            }
        }
        
        // 最终刷新
        db_writer.flush().map_err(|e| format!("最终刷新失败: {}", e))?;
        wal_writer.flush().map_err(|e| format!("最终刷新WAL失败: {}", e))?;
        
        let total_time = start_time.elapsed();
        
        // 计算统计信息
        let min_latency = *latencies.iter().min().unwrap_or(&0);
        let max_latency = *latencies.iter().max().unwrap_or(&0);
        let avg_latency = latencies.iter().sum::<u64>() / latencies.len() as u64;
        let throughput = record_count as f64 / total_time.as_secs_f64();
        
        // 更新统计
        if let Ok(mut stats) = self.stats.lock() {
            stats.total_records = record_count as u64;
            stats.min_latency_ns = min_latency;
            stats.max_latency_ns = max_latency;
            stats.avg_latency_ns = avg_latency;
            stats.throughput_per_sec = throughput;
        }
        
        println!("\n✅ 无压缩写入完成!");
        println!("📈 性能统计:");
        println!("   总记录数: {}", format_number(record_count));
        println!("   总耗时: {:.3} 秒", total_time.as_secs_f64());
        println!("   吞吐量: {:.0} 条/秒", throughput);
        println!("   平均延迟: {:.2} μs", avg_latency as f64 / 1000.0);
        println!("   最小延迟: {:.2} μs", min_latency as f64 / 1000.0);
        println!("   最大延迟: {:.2} μs", max_latency as f64 / 1000.0);
        
        Ok(())
    }
    
    /// LZ4异步压缩策略
    fn strategy_lz4_async(&self, record_count: usize) -> Result<(), String> {
        println!("🔄 使用LZ4异步压缩策略 (平衡性能)");
        
        let db_path = Path::new(&self.output_dir).join("realtime_lz4_v2.duckdb");
        let wal_path = Path::new(&self.output_dir).join("realtime_lz4_v2.duckdb.wal");
        
        // 创建通道用于异步压缩
        let (tx, rx) = mpsc::channel::<Vec<u8>>();
        let db_path_clone = db_path.clone();
        
        // 启动异步压缩线程
        let compression_handle = thread::spawn(move || {
            let mut compressed_writer = BufWriter::new(
                File::create(&db_path_clone).expect("创建压缩文件失败")
            );
            
            let mut total_compressed = 0;
            while let Ok(data) = rx.recv() {
                // 使用LZ4压缩
                let compressed = lz4_flex::compress_prepend_size(&data);
                compressed_writer.write_all(&compressed).expect("写入压缩数据失败");
                total_compressed += 1;
                
                if total_compressed % 1000 == 0 {
                    compressed_writer.flush().expect("刷新压缩文件失败");
                }
            }
            
            compressed_writer.flush().expect("最终刷新失败");
            println!("🗜️ 异步压缩完成，共处理 {} 批数据", total_compressed);
        });
        
        // 主线程进行实时写入
        let mut wal_writer = BufWriter::new(
            File::create(&wal_path).map_err(|e| format!("创建WAL文件失败: {}", e))?
        );
        
        let start_time = Instant::now();
        let mut buffer = Vec::with_capacity(self.buffer_size * 256);
        
        println!("⚡ 开始异步压缩写入测试...");
        
        for i in 0..record_count {
            let record_start = Instant::now();
            
            // 生成记录
            let record = self.generate_realtime_record(i);
            
            // 序列化到缓冲区
            let data = self.serialize_record_fast(&record);
            buffer.extend_from_slice(&data);
            
            // 写入WAL (实时)
            wal_writer.write_u64::<LittleEndian>(record.timestamp)
                .map_err(|e| format!("写入WAL失败: {}", e))?;
            
            // 当缓冲区满时，发送给压缩线程
            if buffer.len() >= self.buffer_size * 256 || i == record_count - 1 {
                tx.send(buffer.clone()).map_err(|e| format!("发送数据失败: {}", e))?;
                buffer.clear();
            }
            
            let latency = record_start.elapsed().as_micros();
            
            if i % 1000 == 0 && i > 0 {
                println!("📊 已写入: {} 条 | 当前延迟: {} μs", i + 1, latency);
            }
            
            // 模拟1毫秒间隔
            if i < record_count - 1 {
                std::thread::sleep(std::time::Duration::from_micros(1000));
            }
        }
        
        // 关闭发送端，等待压缩完成
        drop(tx);
        compression_handle.join().expect("压缩线程结束失败");
        
        wal_writer.flush().map_err(|e| format!("刷新WAL失败: {}", e))?;
        
        let total_time = start_time.elapsed();
        let throughput = record_count as f64 / total_time.as_secs_f64();
        
        println!("\n✅ LZ4异步压缩写入完成!");
        println!("📈 性能统计:");
        println!("   总记录数: {}", format_number(record_count));
        println!("   总耗时: {:.3} 秒", total_time.as_secs_f64());
        println!("   吞吐量: {:.0} 条/秒", throughput);
        println!("   压缩算法: LZ4 (异步)");
        
        Ok(())
    }
    
    /// 执行实时写入测试
    pub fn run_realtime_test(&self, record_count: usize) -> Result<(), String> {
        println!("🎯 实时数据写入性能测试");
        println!("{}", "=".repeat(50));
        
        // 创建输出目录
        create_dir_all(&self.output_dir)
            .map_err(|e| format!("创建目录失败: {}", e))?;
        
        println!("📊 测试配置:");
        println!("   输出目录: {}", self.output_dir);
        println!("   记录数量: {}", format_number(record_count));
        println!("   写入频率: 1000 条/秒 (1毫秒/条)");
        println!("   策略: {:?}", self.strategy);
        
        match self.strategy {
            RealtimeStrategy::NoCompression => {
                self.strategy_no_compression(record_count)
            }
            RealtimeStrategy::LZ4Async => {
                self.strategy_lz4_async(record_count)
            }
            RealtimeStrategy::BufferedBatch => {
                // TODO: 实现批量缓冲策略
                self.strategy_no_compression(record_count)
            }
        }
    }
    
    /// 显示文件信息
    fn show_file_info(&self) -> Result<(), String> {
        println!("\n💾 生成的文件:");
        
        let files = match self.strategy {
            RealtimeStrategy::NoCompression => vec!["realtime_v2.duckdb", "realtime_v2.duckdb.wal"],
            RealtimeStrategy::LZ4Async => vec!["realtime_lz4_v2.duckdb", "realtime_lz4_v2.duckdb.wal"],
            _ => vec!["realtime_v2.duckdb", "realtime_v2.duckdb.wal"],
        };
        
        for file_name in files {
            let file_path = Path::new(&self.output_dir).join(file_name);
            if file_path.exists() {
                let size = file_path.metadata()
                    .map_err(|e| format!("获取文件大小失败: {}", e))?
                    .len() as f64 / (1024.0 * 1024.0);
                println!("   📄 {}: {:.2} MB", file_name, size);
            }
        }
        
        Ok(())
    }
}

/// 格式化数字
fn format_number(n: usize) -> String {
    let s = n.to_string();
    let mut result = String::new();
    for (i, c) in s.chars().rev().enumerate() {
        if i > 0 && i % 3 == 0 {
            result.push(',');
        }
        result.push(c);
    }
    result.chars().rev().collect()
}

fn main() {
    println!("⚡ 实时数据写入优化测试");
    println!("{}", "=".repeat(60));
    
    // 测试配置
    let output_dir = r"D:\database".to_string();
    let record_count = 10_000; // 10秒测试 (1000条/秒 * 10秒)
    let buffer_size = 100;     // 100条记录缓冲
    
    println!("🧪 测试不同策略的性能...\n");
    
    // 测试1: 无压缩直写
    println!("测试1: 无压缩直写策略");
    let generator1 = RealtimeOptimizedGenerator::new(
        output_dir.clone(), 
        RealtimeStrategy::NoCompression, 
        buffer_size
    );
    
    match generator1.run_realtime_test(record_count) {
        Ok(()) => {
            generator1.show_file_info().unwrap_or_default();
        }
        Err(e) => eprintln!("❌ 测试1失败: {}", e),
    }
    
    println!("\n{}", "-".repeat(50));
    
    // 测试2: LZ4异步压缩
    println!("测试2: LZ4异步压缩策略");
    let generator2 = RealtimeOptimizedGenerator::new(
        output_dir.clone(), 
        RealtimeStrategy::LZ4Async, 
        buffer_size
    );
    
    match generator2.run_realtime_test(record_count) {
        Ok(()) => {
            generator2.show_file_info().unwrap_or_default();
        }
        Err(e) => eprintln!("❌ 测试2失败: {}", e),
    }
    
    println!("\n🎉 实时写入测试完成!");
    println!("\n💡 建议:");
    println!("   • 1毫秒1条场景推荐使用无压缩直写");
    println!("   • 如需压缩，推荐LZ4异步压缩");
    println!("   • 可根据存储空间需求选择策略");
}
