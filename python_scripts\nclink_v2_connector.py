#!/usr/bin/env python3
"""
nclink_v2.duckdb Python连接器
提供完整的Python接口来操作nclink_v2.duckdb和nclink_v2.duckdb.wal文件
"""

import sqlite3
import struct
import json
import time
import os
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union
import pandas as pd

class NclinkV2Connector:
    """nclink_v2.duckdb Python连接器"""
    
    def __init__(self, db_path: str = "nclink_v2.duckdb"):
        """
        初始化连接器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.wal_path = self.db_path.with_suffix('.duckdb.wal')
        self.connection = None
        self.is_connected = False
        
        # DuckDB文件格式常量
        self.DUCKDB_MAGIC = b"DUCKDB\x00\x01"
        self.COMPRESSION_TYPES = {
            0: "None", 1: "LZ4", 2: "Gzip", 3: "Snappy", 4: "ZSTD"
        }
    
    def connect(self) -> bool:
        """
        连接到nclink_v2.duckdb数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            if not self.db_path.exists():
                print(f"❌ 数据库文件不存在: {self.db_path}")
                return False
            
            # 验证DuckDB文件格式
            if not self._verify_duckdb_format():
                print(f"❌ 无效的DuckDB文件格式")
                return False
            
            # 使用SQLite兼容模式连接
            # 注意：这里使用SQLite作为兼容层，实际项目中应该使用真正的DuckDB Python库
            self.connection = sqlite3.connect(str(self.db_path))
            self.connection.row_factory = sqlite3.Row
            
            # 设置性能优化参数
            self.connection.execute("PRAGMA journal_mode=WAL")
            self.connection.execute("PRAGMA synchronous=NORMAL")
            self.connection.execute("PRAGMA cache_size=10000")
            
            self.is_connected = True
            print(f"✅ 成功连接到 {self.db_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def _verify_duckdb_format(self) -> bool:
        """验证DuckDB文件格式"""
        try:
            with open(self.db_path, 'rb') as f:
                magic = f.read(8)
                return magic == self.DUCKDB_MAGIC
        except:
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库详细信息
        
        Returns:
            Dict: 数据库信息
        """
        if not self.is_connected:
            raise RuntimeError("数据库未连接，请先调用connect()")
        
        info = {}
        
        try:
            # 读取DuckDB文件头信息
            header_info = self._read_duckdb_header()
            info.update(header_info)
            
            # 获取文件大小信息
            db_size = self.db_path.stat().st_size
            wal_size = self.wal_path.stat().st_size if self.wal_path.exists() else 0
            
            info.update({
                'database_file': str(self.db_path),
                'wal_file': str(self.wal_path),
                'database_size_mb': db_size / (1024 * 1024),
                'wal_size_mb': wal_size / (1024 * 1024),
                'total_size_mb': (db_size + wal_size) / (1024 * 1024),
                'file_exists': {
                    'database': self.db_path.exists(),
                    'wal': self.wal_path.exists()
                }
            })
            
            # 获取表信息（如果是SQLite兼容格式）
            try:
                cursor = self.connection.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                info['tables'] = tables
                
                # 获取每个表的记录数
                table_counts = {}
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    table_counts[table] = count
                info['table_record_counts'] = table_counts
                
            except Exception as e:
                info['tables'] = []
                info['table_record_counts'] = {}
                info['table_error'] = str(e)
            
        except Exception as e:
            info['error'] = str(e)
        
        return info
    
    def _read_duckdb_header(self) -> Dict[str, Any]:
        """读取DuckDB文件头"""
        header_info = {}
        
        try:
            with open(self.db_path, 'rb') as f:
                # 读取魔数
                magic = f.read(8)
                header_info['magic_valid'] = (magic == self.DUCKDB_MAGIC)
                
                # 读取版本
                version = struct.unpack('<I', f.read(4))[0]
                header_info['version'] = version
                
                # 读取页面大小
                page_size = struct.unpack('<I', f.read(4))[0]
                header_info['page_size'] = page_size
                
                # 读取压缩类型
                compression_id = struct.unpack('<I', f.read(4))[0]
                header_info['compression_type'] = self.COMPRESSION_TYPES.get(compression_id, "Unknown")
                
                # 读取时间戳
                created_at = struct.unpack('<Q', f.read(8))[0]
                modified_at = struct.unpack('<Q', f.read(8))[0]
                
                header_info['created_at'] = datetime.fromtimestamp(created_at, tz=timezone.utc).isoformat()
                header_info['modified_at'] = datetime.fromtimestamp(modified_at, tz=timezone.utc).isoformat()
                
                # 读取记录数
                record_count = struct.unpack('<Q', f.read(8))[0]
                header_info['record_count'] = record_count
                
                # 读取压缩比
                compression_ratio = struct.unpack('<d', f.read(8))[0]
                header_info['compression_ratio'] = compression_ratio
                
        except Exception as e:
            header_info['header_read_error'] = str(e)
        
        return header_info
    
    def query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """
        执行SQL查询
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            List[Dict]: 查询结果
        """
        if not self.is_connected:
            raise RuntimeError("数据库未连接，请先调用connect()")
        
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            raise
    
    def query_to_dataframe(self, sql: str, params: Optional[Tuple] = None) -> pd.DataFrame:
        """
        执行查询并返回pandas DataFrame
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            pd.DataFrame: 查询结果
        """
        if not self.is_connected:
            raise RuntimeError("数据库未连接，请先调用connect()")
        
        try:
            if params:
                return pd.read_sql_query(sql, self.connection, params=params)
            else:
                return pd.read_sql_query(sql, self.connection)
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            raise
    
    def get_engineering_data(self, 
                           point_id_pattern: Optional[str] = None,
                           device_id: Optional[str] = None,
                           start_time: Optional[int] = None,
                           end_time: Optional[int] = None,
                           limit: int = 1000) -> List[Dict[str, Any]]:
        """
        获取工程数据
        
        Args:
            point_id_pattern: 测点ID模式
            device_id: 设备ID
            start_time: 开始时间戳
            end_time: 结束时间戳
            limit: 返回记录数限制
            
        Returns:
            List[Dict]: 工程数据
        """
        conditions = []
        params = []
        
        if point_id_pattern:
            conditions.append("point_id LIKE ?")
            params.append(f"%{point_id_pattern}%")
        
        if device_id:
            conditions.append("device_id = ?")
            params.append(device_id)
        
        if start_time:
            conditions.append("timestamp >= ?")
            params.append(start_time)
        
        if end_time:
            conditions.append("timestamp <= ?")
            params.append(end_time)
        
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        sql = f"""
        SELECT point_id, timestamp, value, quality, device_id, tags
        FROM engineering_data 
        WHERE {where_clause}
        ORDER BY timestamp DESC
        LIMIT ?
        """
        
        params.append(limit)
        
        return self.query(sql, tuple(params))
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Returns:
            Dict: 统计信息
        """
        if not self.is_connected:
            raise RuntimeError("数据库未连接，请先调用connect()")
        
        stats = {}
        
        try:
            # 基础统计
            basic_stats = self.query("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT point_id) as unique_points,
                    COUNT(DISTINCT device_id) as unique_devices,
                    MIN(timestamp) as min_timestamp,
                    MAX(timestamp) as max_timestamp,
                    AVG(value) as avg_value,
                    MIN(value) as min_value,
                    MAX(value) as max_value
                FROM engineering_data
            """)[0]
            
            stats.update(basic_stats)
            
            # 质量码分布
            quality_stats = self.query("""
                SELECT quality, COUNT(*) as count
                FROM engineering_data 
                GROUP BY quality 
                ORDER BY quality
            """)
            
            stats['quality_distribution'] = {
                row['quality']: row['count'] for row in quality_stats
            }
            
            # 设备分布
            device_stats = self.query("""
                SELECT device_id, COUNT(*) as count
                FROM engineering_data 
                GROUP BY device_id 
                ORDER BY count DESC
                LIMIT 10
            """)
            
            stats['top_devices'] = {
                row['device_id']: row['count'] for row in device_stats
            }
            
            # 时间范围
            if stats['min_timestamp'] and stats['max_timestamp']:
                stats['time_range_hours'] = (stats['max_timestamp'] - stats['min_timestamp']) / 3600
                stats['start_time'] = datetime.fromtimestamp(stats['min_timestamp']).isoformat()
                stats['end_time'] = datetime.fromtimestamp(stats['max_timestamp']).isoformat()
            
        except Exception as e:
            stats['error'] = str(e)
        
        return stats
    
    def analyze_point_trends(self, point_id: str, hours: int = 24) -> Dict[str, Any]:
        """
        分析测点趋势
        
        Args:
            point_id: 测点ID
            hours: 分析时间范围（小时）
            
        Returns:
            Dict: 趋势分析结果
        """
        end_time = int(time.time())
        start_time = end_time - (hours * 3600)
        
        data = self.query("""
            SELECT timestamp, value, quality
            FROM engineering_data 
            WHERE point_id = ? AND timestamp BETWEEN ? AND ?
            ORDER BY timestamp
        """, (point_id, start_time, end_time))
        
        if not data:
            return {'error': f'测点 {point_id} 在最近 {hours} 小时内无数据'}
        
        values = [row['value'] for row in data if row['quality'] == 192]  # 只分析好质量数据
        
        if len(values) < 2:
            return {'error': '数据点不足，无法进行趋势分析'}
        
        # 计算趋势
        import statistics
        
        # 简单线性趋势
        x = list(range(len(values)))
        n = len(values)
        sum_x = sum(x)
        sum_y = sum(values)
        sum_xy = sum(x[i] * values[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
        
        trend_direction = 'increasing' if slope > 0.01 else 'decreasing' if slope < -0.01 else 'stable'
        
        return {
            'point_id': point_id,
            'data_points': len(data),
            'good_quality_points': len(values),
            'time_range_hours': hours,
            'statistics': {
                'min': min(values),
                'max': max(values),
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'std': statistics.stdev(values) if len(values) > 1 else 0
            },
            'trend': {
                'direction': trend_direction,
                'slope': slope,
                'change_rate': slope * len(values)
            }
        }
    
    def export_to_csv(self, output_file: str, 
                     point_id_pattern: Optional[str] = None,
                     limit: int = 10000) -> bool:
        """
        导出数据到CSV文件
        
        Args:
            output_file: 输出文件路径
            point_id_pattern: 测点ID模式
            limit: 导出记录数限制
            
        Returns:
            bool: 导出是否成功
        """
        try:
            if point_id_pattern:
                df = self.query_to_dataframe("""
                    SELECT point_id, timestamp, value, quality, device_id, tags
                    FROM engineering_data 
                    WHERE point_id LIKE ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                """, (f"%{point_id_pattern}%", limit))
            else:
                df = self.query_to_dataframe("""
                    SELECT point_id, timestamp, value, quality, device_id, tags
                    FROM engineering_data 
                    ORDER BY timestamp DESC
                    LIMIT ?
                """, (limit,))
            
            # 转换时间戳为可读格式
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
            
            df.to_csv(output_file, index=False)
            print(f"✅ 成功导出 {len(df)} 条记录到 {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return False
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.is_connected = False
            print("🔒 数据库连接已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()

def main():
    """演示使用方法"""
    print("🔗 nclink_v2.duckdb Python连接器演示")
    print("=" * 50)
    
    # 检查文件是否存在
    db_files = ["nclink_v2.duckdb", "engineering_data.db"]
    db_file = None
    
    for file in db_files:
        if Path(file).exists():
            db_file = file
            break
    
    if not db_file:
        print("❌ 未找到数据库文件")
        print("💡 请先运行以下命令生成数据库:")
        print("   cargo run --bin duckdb_compiler")
        print("   或")
        print("   python python_scripts/engineering_data_inserter.py")
        return
    
    print(f"📁 使用数据库文件: {db_file}")
    
    # 使用上下文管理器连接数据库
    with NclinkV2Connector(db_file) as db:
        if not db.is_connected:
            print("❌ 连接失败")
            return
        
        # 获取数据库信息
        print("\n📊 数据库信息:")
        info = db.get_database_info()
        
        for key, value in info.items():
            if key == 'file_exists':
                print(f"   文件存在状态:")
                for file_type, exists in value.items():
                    status = "✅" if exists else "❌"
                    print(f"     {status} {file_type}")
            elif key in ['database_size_mb', 'wal_size_mb', 'total_size_mb']:
                print(f"   {key}: {value:.2f} MB")
            elif key == 'compression_ratio':
                print(f"   {key}: {value:.1f}:1")
            elif key not in ['table_record_counts', 'tables']:
                print(f"   {key}: {value}")
        
        # 获取统计信息
        print("\n📈 数据统计:")
        stats = db.get_statistics()
        
        if 'error' not in stats:
            print(f"   总记录数: {stats.get('total_records', 0):,}")
            print(f"   唯一测点: {stats.get('unique_points', 0):,}")
            print(f"   唯一设备: {stats.get('unique_devices', 0):,}")
            
            if 'time_range_hours' in stats:
                print(f"   时间跨度: {stats['time_range_hours']:.1f} 小时")
            
            print(f"   数值范围: {stats.get('min_value', 0):.2f} - {stats.get('max_value', 0):.2f}")
        
        # 查询示例数据
        print("\n🔍 示例查询:")
        try:
            recent_data = db.get_engineering_data(limit=5)
            
            if recent_data:
                print("   最新5条记录:")
                for record in recent_data:
                    dt = datetime.fromtimestamp(record['timestamp'])
                    print(f"     {record['point_id']}: {record['value']:.2f} @ {dt}")
            else:
                print("   ❌ 未找到数据")
        except Exception as e:
            print(f"   ❌ 查询失败: {e}")
        
        print("\n🎉 连接演示完成!")

if __name__ == "__main__":
    main()
