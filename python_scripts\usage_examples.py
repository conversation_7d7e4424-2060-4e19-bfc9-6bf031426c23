#!/usr/bin/env python3
"""
nclink_v2.duckdb Python连接使用示例
展示各种常见的数据操作和查询方法
"""

from nclink_v2_connector import NclinkV2Connector
from datetime import datetime, timedelta
import pandas as pd
import json

def example_basic_connection():
    """示例1: 基础连接和信息获取"""
    print("📝 示例1: 基础连接和信息获取")
    print("-" * 40)
    
    # 方法1: 手动连接
    db = NclinkV2Connector("engineering_data.db")
    
    if db.connect():
        # 获取数据库信息
        info = db.get_database_info()
        print(f"✅ 数据库文件: {info.get('database_file')}")
        print(f"📊 压缩算法: {info.get('compression_type')}")
        print(f"📈 记录数: {info.get('record_count')}")
        print(f"💾 文件大小: {info.get('database_size_mb', 0):.2f} MB")
        
        db.close()
    
    # 方法2: 使用上下文管理器（推荐）
    with NclinkV2Connector("engineering_data.db") as db:
        if db.is_connected:
            stats = db.get_statistics()
            print(f"📊 总记录数: {stats.get('total_records', 0):,}")
            print(f"🔧 唯一测点: {stats.get('unique_points', 0):,}")

def example_data_queries():
    """示例2: 数据查询操作"""
    print("\n📝 示例2: 数据查询操作")
    print("-" * 40)
    
    with NclinkV2Connector("engineering_data.db") as db:
        if not db.is_connected:
            print("❌ 连接失败")
            return
        
        # 1. 基础SQL查询
        print("🔍 基础SQL查询:")
        recent_data = db.query("""
            SELECT point_id, timestamp, value, quality, device_id
            FROM engineering_data 
            ORDER BY timestamp DESC 
            LIMIT 5
        """)
        
        for record in recent_data:
            dt = datetime.fromtimestamp(record['timestamp'])
            print(f"   {record['point_id']}: {record['value']:.2f} @ {dt}")
        
        # 2. 参数化查询
        print("\n🔍 参数化查询 (查找温度测点):")
        temp_data = db.query("""
            SELECT point_id, AVG(value) as avg_temp, COUNT(*) as count
            FROM engineering_data 
            WHERE point_id LIKE ? AND quality = ?
            GROUP BY point_id
            ORDER BY avg_temp DESC
            LIMIT 3
        """, ("%TI%", 192))
        
        for record in temp_data:
            print(f"   {record['point_id']}: 平均温度 {record['avg_temp']:.2f}°C ({record['count']} 条记录)")
        
        # 3. 使用便捷方法
        print("\n🔍 便捷查询方法:")
        engineering_data = db.get_engineering_data(
            point_id_pattern="Plant_A",
            limit=3
        )
        
        for record in engineering_data:
            dt = datetime.fromtimestamp(record['timestamp'])
            print(f"   {record['point_id']}: {record['value']:.2f} @ {dt}")

def example_pandas_integration():
    """示例3: Pandas集成"""
    print("\n📝 示例3: Pandas集成")
    print("-" * 40)
    
    with NclinkV2Connector("engineering_data.db") as db:
        if not db.is_connected:
            print("❌ 连接失败")
            return
        
        # 1. 查询到DataFrame
        print("📊 查询数据到DataFrame:")
        df = db.query_to_dataframe("""
            SELECT 
                device_id,
                COUNT(*) as record_count,
                AVG(value) as avg_value,
                MIN(value) as min_value,
                MAX(value) as max_value
            FROM engineering_data 
            WHERE quality = 192
            GROUP BY device_id
            ORDER BY record_count DESC
        """)
        
        print(f"   DataFrame形状: {df.shape}")
        print("   设备统计:")
        print(df.to_string(index=False))
        
        # 2. 时间序列数据
        print("\n📈 时间序列数据:")
        ts_df = db.query_to_dataframe("""
            SELECT 
                timestamp,
                value,
                point_id
            FROM engineering_data 
            WHERE point_id LIKE '%TI%' 
            AND quality = 192
            ORDER BY timestamp DESC
            LIMIT 100
        """)
        
        if not ts_df.empty:
            # 转换时间戳
            ts_df['datetime'] = pd.to_datetime(ts_df['timestamp'], unit='s')
            
            # 基础统计
            print(f"   时间范围: {ts_df['datetime'].min()} 到 {ts_df['datetime'].max()}")
            print(f"   数值范围: {ts_df['value'].min():.2f} - {ts_df['value'].max():.2f}")
            print(f"   平均值: {ts_df['value'].mean():.2f}")

def example_trend_analysis():
    """示例4: 趋势分析"""
    print("\n📝 示例4: 趋势分析")
    print("-" * 40)
    
    with NclinkV2Connector("engineering_data.db") as db:
        if not db.is_connected:
            print("❌ 连接失败")
            return
        
        # 获取一个测点进行趋势分析
        points = db.query("""
            SELECT DISTINCT point_id 
            FROM engineering_data 
            WHERE point_id LIKE '%TI%'
            LIMIT 1
        """)
        
        if points:
            point_id = points[0]['point_id']
            print(f"🔍 分析测点: {point_id}")
            
            # 使用内置趋势分析
            trend = db.analyze_point_trends(point_id, hours=24)
            
            if 'error' not in trend:
                print(f"   数据点数: {trend['data_points']}")
                print(f"   好质量数据: {trend['good_quality_points']}")
                print(f"   数值范围: {trend['statistics']['min']:.2f} - {trend['statistics']['max']:.2f}")
                print(f"   平均值: {trend['statistics']['mean']:.2f}")
                print(f"   趋势方向: {trend['trend']['direction']}")
                print(f"   变化率: {trend['trend']['change_rate']:.4f}")
            else:
                print(f"   ❌ {trend['error']}")

def example_data_export():
    """示例5: 数据导出"""
    print("\n📝 示例5: 数据导出")
    print("-" * 40)
    
    with NclinkV2Connector("engineering_data.db") as db:
        if not db.is_connected:
            print("❌ 连接失败")
            return
        
        # 1. 导出到CSV
        print("💾 导出温度数据到CSV:")
        success = db.export_to_csv(
            "temperature_data.csv",
            point_id_pattern="TI",
            limit=1000
        )
        
        if success:
            print("   ✅ 导出成功: temperature_data.csv")
        
        # 2. 自定义导出
        print("\n💾 自定义数据导出:")
        df = db.query_to_dataframe("""
            SELECT 
                point_id,
                timestamp,
                value,
                quality,
                device_id,
                datetime(timestamp, 'unixepoch') as readable_time
            FROM engineering_data 
            WHERE device_id = 'Plant_A_Unit_01_Reactor_PLC'
            ORDER BY timestamp DESC
            LIMIT 500
        """)
        
        if not df.empty:
            # 导出到Excel
            try:
                df.to_excel("plant_a_reactor_data.xlsx", index=False)
                print("   ✅ 导出成功: plant_a_reactor_data.xlsx")
            except:
                print("   ⚠️ Excel导出需要安装openpyxl: pip install openpyxl")
            
            # 导出到JSON
            df.to_json("plant_a_reactor_data.json", orient='records', indent=2)
            print("   ✅ 导出成功: plant_a_reactor_data.json")

def example_advanced_queries():
    """示例6: 高级查询"""
    print("\n📝 示例6: 高级查询")
    print("-" * 40)
    
    with NclinkV2Connector("engineering_data.db") as db:
        if not db.is_connected:
            print("❌ 连接失败")
            return
        
        # 1. 聚合查询
        print("📊 按小时聚合数据:")
        hourly_data = db.query("""
            SELECT 
                strftime('%Y-%m-%d %H:00', datetime(timestamp, 'unixepoch')) as hour,
                COUNT(*) as record_count,
                AVG(value) as avg_value,
                MIN(value) as min_value,
                MAX(value) as max_value,
                AVG(CASE WHEN quality = 192 THEN 1.0 ELSE 0.0 END) * 100 as good_quality_pct
            FROM engineering_data 
            GROUP BY hour
            ORDER BY hour DESC
            LIMIT 5
        """)
        
        for record in hourly_data:
            print(f"   {record['hour']}: {record['record_count']} 条记录, "
                  f"平均值: {record['avg_value']:.2f}, "
                  f"质量: {record['good_quality_pct']:.1f}%")
        
        # 2. 窗口函数查询
        print("\n📈 移动平均计算:")
        moving_avg = db.query("""
            SELECT 
                point_id,
                timestamp,
                value,
                AVG(value) OVER (
                    PARTITION BY point_id 
                    ORDER BY timestamp 
                    ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
                ) as moving_avg_5
            FROM engineering_data 
            WHERE point_id LIKE '%TI%' 
            AND quality = 192
            ORDER BY point_id, timestamp DESC
            LIMIT 10
        """)
        
        for record in moving_avg:
            dt = datetime.fromtimestamp(record['timestamp'])
            print(f"   {record['point_id']}: 当前值 {record['value']:.2f}, "
                  f"5点移动平均 {record['moving_avg_5']:.2f} @ {dt}")

def example_error_handling():
    """示例7: 错误处理"""
    print("\n📝 示例7: 错误处理")
    print("-" * 40)
    
    # 1. 文件不存在的处理
    print("🔍 处理文件不存在:")
    db = NclinkV2Connector("nonexistent.db")
    if not db.connect():
        print("   ✅ 正确处理了文件不存在的情况")
    
    # 2. SQL错误处理
    print("\n🔍 处理SQL错误:")
    with NclinkV2Connector("engineering_data.db") as db:
        if db.is_connected:
            try:
                # 故意写错误的SQL
                result = db.query("SELECT * FROM nonexistent_table")
            except Exception as e:
                print(f"   ✅ 正确捕获SQL错误: {type(e).__name__}")
    
    # 3. 连接状态检查
    print("\n🔍 连接状态检查:")
    db = NclinkV2Connector("engineering_data.db")
    try:
        # 未连接时查询
        db.query("SELECT 1")
    except RuntimeError as e:
        print(f"   ✅ 正确检查连接状态: {e}")

def main():
    """运行所有示例"""
    print("🚀 nclink_v2.duckdb Python连接使用示例")
    print("=" * 60)
    
    # 检查数据库文件
    from pathlib import Path
    if not Path("engineering_data.db").exists():
        print("❌ 未找到 engineering_data.db 文件")
        print("💡 请先运行: python python_scripts/engineering_data_inserter.py")
        return
    
    try:
        # 运行所有示例
        example_basic_connection()
        example_data_queries()
        example_pandas_integration()
        example_trend_analysis()
        example_data_export()
        example_advanced_queries()
        example_error_handling()
        
        print("\n🎉 所有示例运行完成!")
        print("\n💡 生成的文件:")
        print("   - temperature_data.csv")
        print("   - plant_a_reactor_data.xlsx (如果安装了openpyxl)")
        print("   - plant_a_reactor_data.json")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")

if __name__ == "__main__":
    main()
