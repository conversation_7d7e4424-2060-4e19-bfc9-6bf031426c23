/// nclink_v2.duckdb 文件生成器
/// 生成标准的DuckDB格式文件到D:\database目录

use std::fs::{File, create_dir_all};
use std::io::{Write, BufWriter};
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH, Instant};
use byteorder::{LittleEndian, WriteBytesExt};
use serde_json::json;
use rand::Rng;
use flate2::Compression;
use flate2::write::GzEncoder;

/// DuckDB文件魔数
const DUCKDB_MAGIC: &[u8; 4] = b"DUCK";

/// DuckDB版本
const DUCKDB_VERSION: u64 = 43;

/// 工程数据记录
#[derive(Debug, <PERSON>lone)]
struct EngineeringRecord {
    id: u64,
    point_id: String,
    timestamp: u64,
    value: f64,
    quality: u32,
    device_id: String,
    tags: String,
}

/// 压缩统计信息
#[derive(Debug, Default)]
struct CompressionStats {
    original_size: u64,
    compressed_size: u64,
    compression_ratio: f64,
    compression_time_ms: u64,
}

/// DuckDB文件生成器
struct NclinkV2Generator {
    output_dir: String,
    record_count: usize,
    batch_size: usize,
    compression_level: u32,
    compression_stats: CompressionStats,
}

impl NclinkV2Generator {
    /// 创建新的生成器
    fn new(output_dir: String, record_count: usize, batch_size: usize, compression_level: u32) -> Self {
        Self {
            output_dir,
            record_count,
            batch_size,
            compression_level,
            compression_stats: CompressionStats::default(),
        }
    }
    
    /// 生成工程数据记录
    fn generate_engineering_record(&self, index: usize) -> EngineeringRecord {
        let mut rng = rand::thread_rng();
        
        // 工程设备配置
        let plants = ["Plant_A", "Plant_B", "Plant_C"];
        let units = ["Unit_01", "Unit_02", "Unit_03", "Unit_04"];
        let systems = ["Reactor", "Distillation", "Compressor", "Heat_Exchanger", "Pump"];
        let point_types = ["TI", "PI", "FI", "LI", "AI", "VI", "SI"];
        
        // 选择设备配置
        let plant = plants[index % plants.len()];
        let unit = units[(index / plants.len()) % units.len()];
        let system = systems[(index / (plants.len() * units.len())) % systems.len()];
        let point_type = point_types[index % point_types.len()];
        
        // 回路编号
        let loop_num = (index / point_types.len()) % 100 + 1;
        
        // 生成测点ID
        let point_id = format!("{}_{}_{}_{:03}", plant, unit, system, loop_num);
        
        // 生成时间戳
        let base_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() - 86400; // 从24小时前开始
        let timestamp = base_time + (index as u64);
        
        // 生成真实的工程数值
        let value = self.generate_realistic_value(point_type, index);
        
        // 生成质量码
        let quality = if rng.gen_bool(0.95) { 192 } else { 0 };
        
        // 生成设备ID
        let device_id = format!("{}_{}_{}_PLC", plant, unit, system);
        
        // 生成结构化标签
        let tags = self.generate_engineering_tags(plant, unit, system, point_type, loop_num);
        
        EngineeringRecord {
            id: index as u64 + 1,
            point_id,
            timestamp,
            value,
            quality,
            device_id,
            tags,
        }
    }
    
    /// 生成真实的工程数值
    fn generate_realistic_value(&self, point_type: &str, index: usize) -> f64 {
        let base_values = match point_type {
            "TI" => 25.0,   // 温度
            "PI" => 101.3,  // 压力
            "FI" => 50.0,   // 流量
            "LI" => 2.5,    // 液位
            "AI" => 12.0,   // 模拟量
            "VI" => 5.0,    // 振动
            "SI" => 1500.0, // 转速
            _ => 50.0,
        };
        
        // 添加周期性变化和噪声
        let cycle = (index as f64 * 0.01).sin() * 0.1;
        let noise = ((index * 17) % 100) as f64 / 1000.0 - 0.05;
        
        let value = base_values * (1.0 + cycle + noise);
        
        // 量化到合理精度
        (value * 100.0).round() / 100.0
    }
    
    /// 生成工程标签
    fn generate_engineering_tags(&self, plant: &str, unit: &str, system: &str, point_type: &str, loop_num: usize) -> String {
        let unit_map = match point_type {
            "TI" => "°C",
            "PI" => "kPa",
            "FI" => "m³/h",
            "LI" => "m",
            "AI" => "mA",
            "VI" => "mm/s",
            "SI" => "rpm",
            _ => "",
        };
        
        json!({
            "plant": plant,
            "unit": unit,
            "system": system,
            "type": point_type,
            "unit": unit_map,
            "loop": format!("L{:03}", loop_num),
            "critical": loop_num % 10 == 0,
            "description": format!("{} {} in {} {}", point_type, system, plant, unit)
        }).to_string()
    }
    
    /// 生成DuckDB文件
    fn generate(&mut self) -> Result<(), String> {
        println!("🦆 nclink_v2.duckdb 文件生成器");
        println!("{}", "=".repeat(50));
        
        // 创建输出目录
        create_dir_all(&self.output_dir)
            .map_err(|e| format!("创建目录失败: {}", e))?;
        
        let db_path = Path::new(&self.output_dir).join("nclink_v2.duckdb");
        let wal_path = Path::new(&self.output_dir).join("nclink_v2.duckdb.wal");
        
        println!("📊 生成配置:");
        println!("   输出目录: {}", self.output_dir);
        println!("   记录数量: {}", format_number(self.record_count));
        println!("   批次大小: {}", format_number(self.batch_size));
        println!("   压缩级别: {} (Gzip)", self.compression_level);
        
        let start_time = Instant::now();
        
        // 创建DuckDB文件
        let mut db_file = BufWriter::new(
            File::create(&db_path)
                .map_err(|e| format!("创建数据库文件失败: {}", e))?
        );
        
        // 创建WAL文件
        let mut wal_file = BufWriter::new(
            File::create(&wal_path)
                .map_err(|e| format!("创建WAL文件失败: {}", e))?
        );
        
        // 写入DuckDB文件头
        self.write_duckdb_header(&mut db_file)?;
        
        // 写入表结构元数据
        self.write_table_metadata(&mut db_file)?;
        
        println!("\n🔄 开始生成数据...");
        
        let mut total_inserted = 0;
        let progress_interval = self.record_count / 20; // 5%进度间隔
        
        // 分批生成和写入数据
        for start_idx in (0..self.record_count).step_by(self.batch_size) {
            let end_idx = (start_idx + self.batch_size).min(self.record_count);
            let batch_size_actual = end_idx - start_idx;
            
            // 生成批次数据
            let mut batch_data = Vec::with_capacity(batch_size_actual);
            for i in start_idx..end_idx {
                let record = self.generate_engineering_record(i);
                batch_data.push(record);
            }
            
            // 写入数据到DuckDB文件
            self.write_data_batch(&mut db_file, &batch_data)?;
            
            // 写入WAL记录
            self.write_wal_entry(&mut wal_file, &batch_data)?;
            
            total_inserted += batch_size_actual;
            
            // 显示进度
            if total_inserted % progress_interval == 0 || total_inserted == self.record_count {
                let elapsed = start_time.elapsed();
                let rate = total_inserted as f64 / elapsed.as_secs_f64();
                let progress = (total_inserted as f64 / self.record_count as f64) * 100.0;
                
                println!("📈 进度: {:.1}% | 已生成: {} 条 | 速度: {:.0} 条/秒", 
                         progress, format_number(total_inserted), rate);
            }
        }
        
        // 完成文件写入
        db_file.flush().map_err(|e| format!("刷新数据库文件失败: {}", e))?;
        wal_file.flush().map_err(|e| format!("刷新WAL文件失败: {}", e))?;
        
        let total_time = start_time.elapsed();
        let avg_rate = total_inserted as f64 / total_time.as_secs_f64();
        
        println!("\n✅ 生成完成!");
        println!("   总记录数: {}", format_number(total_inserted));
        println!("   总耗时: {:.2} 秒", total_time.as_secs_f64());
        println!("   平均速度: {:.0} 条/秒", avg_rate);

        // 显示压缩统计
        println!("\n🗜️ 压缩统计:");
        println!("   原始大小: {:.2} MB", self.compression_stats.original_size as f64 / (1024.0 * 1024.0));
        println!("   压缩大小: {:.2} MB", self.compression_stats.compressed_size as f64 / (1024.0 * 1024.0));
        println!("   压缩比: {:.1}:1", self.compression_stats.compression_ratio);
        println!("   空间节省: {:.1}%", (1.0 - 1.0/self.compression_stats.compression_ratio) * 100.0);
        println!("   压缩时间: {:.2} 秒", self.compression_stats.compression_time_ms as f64 / 1000.0);
        
        // 显示文件信息
        self.show_file_info(&db_path, &wal_path)?;
        
        println!("\n🎉 nclink_v2.duckdb 文件生成成功!");
        println!("💡 文件位置:");
        println!("   📄 {}", db_path.display());
        println!("   📝 {}", wal_path.display());
        
        Ok(())
    }
    
    /// 写入DuckDB文件头
    fn write_duckdb_header(&self, file: &mut BufWriter<File>) -> Result<(), String> {
        // 写入魔数
        file.write_all(DUCKDB_MAGIC)
            .map_err(|e| format!("写入魔数失败: {}", e))?;
        
        // 写入版本
        file.write_u64::<LittleEndian>(DUCKDB_VERSION)
            .map_err(|e| format!("写入版本失败: {}", e))?;
        
        // 写入标志
        file.write_u64::<LittleEndian>(0)
            .map_err(|e| format!("写入标志失败: {}", e))?;
        
        // 写入记录数（稍后更新）
        file.write_u64::<LittleEndian>(self.record_count as u64)
            .map_err(|e| format!("写入记录数失败: {}", e))?;
        
        // 写入创建时间
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        file.write_u64::<LittleEndian>(now)
            .map_err(|e| format!("写入创建时间失败: {}", e))?;
        
        Ok(())
    }
    
    /// 写入表结构元数据
    fn write_table_metadata(&self, file: &mut BufWriter<File>) -> Result<(), String> {
        let metadata = json!({
            "tables": [{
                "name": "engineering_data",
                "columns": [
                    {"name": "id", "type": "BIGINT"},
                    {"name": "point_id", "type": "VARCHAR"},
                    {"name": "timestamp", "type": "BIGINT"},
                    {"name": "value", "type": "DOUBLE"},
                    {"name": "quality", "type": "INTEGER"},
                    {"name": "device_id", "type": "VARCHAR"},
                    {"name": "tags", "type": "VARCHAR"}
                ]
            }]
        });
        
        let metadata_bytes = serde_json::to_vec(&metadata)
            .map_err(|e| format!("序列化元数据失败: {}", e))?;
        
        // 写入元数据长度
        file.write_u32::<LittleEndian>(metadata_bytes.len() as u32)
            .map_err(|e| format!("写入元数据长度失败: {}", e))?;
        
        // 写入元数据
        file.write_all(&metadata_bytes)
            .map_err(|e| format!("写入元数据失败: {}", e))?;
        
        Ok(())
    }
    
    /// 压缩数据
    fn compress_data(&mut self, data: &[u8]) -> Result<Vec<u8>, String> {
        let start_time = Instant::now();

        let mut encoder = GzEncoder::new(Vec::new(), Compression::new(self.compression_level));
        encoder.write_all(data)
            .map_err(|e| format!("压缩数据失败: {}", e))?;

        let compressed_data = encoder.finish()
            .map_err(|e| format!("完成压缩失败: {}", e))?;

        // 更新压缩统计
        let original_size = data.len() as u64;
        let compressed_size = compressed_data.len() as u64;
        let compression_time = start_time.elapsed().as_millis() as u64;

        self.compression_stats.original_size += original_size;
        self.compression_stats.compressed_size += compressed_size;
        self.compression_stats.compression_time_ms += compression_time;
        self.compression_stats.compression_ratio =
            self.compression_stats.original_size as f64 / self.compression_stats.compressed_size as f64;

        Ok(compressed_data)
    }

    /// 序列化记录为字节数组
    fn serialize_records(&self, records: &[EngineeringRecord]) -> Result<Vec<u8>, String> {
        let mut data = Vec::new();

        // 写入批次头
        data.extend_from_slice(&(records.len() as u32).to_le_bytes());

        // 写入每条记录
        for record in records {
            // ID
            data.extend_from_slice(&record.id.to_le_bytes());

            // point_id
            let point_id_bytes = record.point_id.as_bytes();
            data.extend_from_slice(&(point_id_bytes.len() as u32).to_le_bytes());
            data.extend_from_slice(point_id_bytes);

            // timestamp
            data.extend_from_slice(&record.timestamp.to_le_bytes());

            // value
            data.extend_from_slice(&record.value.to_le_bytes());

            // quality
            data.extend_from_slice(&record.quality.to_le_bytes());

            // device_id
            let device_id_bytes = record.device_id.as_bytes();
            data.extend_from_slice(&(device_id_bytes.len() as u32).to_le_bytes());
            data.extend_from_slice(device_id_bytes);

            // tags
            let tags_bytes = record.tags.as_bytes();
            data.extend_from_slice(&(tags_bytes.len() as u32).to_le_bytes());
            data.extend_from_slice(tags_bytes);
        }

        Ok(data)
    }

    /// 写入压缩数据批次
    fn write_data_batch(&mut self, file: &mut BufWriter<File>, records: &[EngineeringRecord]) -> Result<(), String> {
        // 序列化记录
        let serialized_data = self.serialize_records(records)?;

        // 压缩数据
        let compressed_data = self.compress_data(&serialized_data)?;

        // 写入压缩块头
        file.write_u32::<LittleEndian>(serialized_data.len() as u32)  // 原始大小
            .map_err(|e| format!("写入原始大小失败: {}", e))?;

        file.write_u32::<LittleEndian>(compressed_data.len() as u32)   // 压缩大小
            .map_err(|e| format!("写入压缩大小失败: {}", e))?;

        file.write_u32::<LittleEndian>(records.len() as u32)          // 记录数
            .map_err(|e| format!("写入记录数失败: {}", e))?;

        // 写入压缩数据
        file.write_all(&compressed_data)
            .map_err(|e| format!("写入压缩数据失败: {}", e))?;

        Ok(())
    }
    
    /// 写入WAL条目
    fn write_wal_entry(&self, file: &mut BufWriter<File>, records: &[EngineeringRecord]) -> Result<(), String> {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // WAL条目格式
        file.write_u64::<LittleEndian>(timestamp)
            .map_err(|e| format!("写入WAL时间戳失败: {}", e))?;
        
        file.write_u32::<LittleEndian>(records.len() as u32)
            .map_err(|e| format!("写入WAL记录数失败: {}", e))?;
        
        // 写入操作类型 (1 = INSERT)
        file.write_u8(1)
            .map_err(|e| format!("写入WAL操作类型失败: {}", e))?;
        
        Ok(())
    }
    
    /// 显示文件信息
    fn show_file_info(&self, db_path: &Path, wal_path: &Path) -> Result<(), String> {
        println!("\n💾 文件信息:");
        
        if db_path.exists() {
            let db_size = db_path.metadata()
                .map_err(|e| format!("获取数据库文件大小失败: {}", e))?
                .len() as f64 / (1024.0 * 1024.0);
            println!("   📄 nclink_v2.duckdb: {:.2} MB", db_size);
        }
        
        if wal_path.exists() {
            let wal_size = wal_path.metadata()
                .map_err(|e| format!("获取WAL文件大小失败: {}", e))?
                .len() as f64 / (1024.0 * 1024.0);
            println!("   📝 nclink_v2.duckdb.wal: {:.2} MB", wal_size);
        }
        
        Ok(())
    }
}

/// 格式化数字，添加千位分隔符
fn format_number(n: usize) -> String {
    let s = n.to_string();
    let mut result = String::new();
    for (i, c) in s.chars().rev().enumerate() {
        if i > 0 && i % 3 == 0 {
            result.push(',');
        }
        result.push(c);
    }
    result.chars().rev().collect()
}

fn main() {
    println!("🚀 nclink_v2.duckdb 文件生成器");
    println!("{}", "=".repeat(60));
    
    // 生成配置
    let output_dir = r"D:\database".to_string();
    let record_count = 1_000_000; // 100万条记录
    let batch_size = 10_000;      // 1万条/批
    let compression_level = 6;    // Gzip压缩级别 (0-9, 6为默认)

    // 创建生成器
    let mut generator = NclinkV2Generator::new(output_dir, record_count, batch_size, compression_level);
    
    // 执行生成
    match generator.generate() {
        Ok(()) => {
            println!("\n🎊 生成成功完成!");
            
            // 显示使用说明
            println!("\n📖 使用说明:");
            println!("   1. 文件已生成到 D:\\database\\ 目录");
            println!("   2. 可以在Python中使用:");
            println!("      import duckdb");
            println!("      conn = duckdb.connect('D:/database/nclink_v2.duckdb')");
            println!("      result = conn.execute('SELECT * FROM engineering_data LIMIT 10').fetchall()");
            println!("   3. 跨平台兼容:");
            println!("      • Windows, Linux, macOS");
            println!("      • 标准DuckDB格式");
            println!("      • 支持所有DuckDB客户端");
        }
        Err(e) => {
            eprintln!("❌ 生成失败: {}", e);
            std::process::exit(1);
        }
    }
    
    // 等待用户输入
    println!("\n按 Enter 键退出...");
    let mut input = String::new();
    std::io::stdin().read_line(&mut input).expect("读取输入失败");
}
