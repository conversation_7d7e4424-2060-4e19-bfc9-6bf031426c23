#!/usr/bin/env python3
"""
测试内存DuckDB数据库功能
"""

import duckdb
import json
from datetime import datetime

def test_memory_database():
    """测试内存数据库基本功能"""
    print("🧪 测试内存DuckDB数据库...")
    
    try:
        # 创建命名内存数据库
        conn = duckdb.connect(':memory:nc-link-v2')
        print("✅ 内存数据库创建成功: nc-link-v2")
        
        # 创建表结构
        conn.execute("""
            CREATE TABLE nclink_data (
                id BIGINT PRIMARY KEY,
                timestamp BIGINT NOT NULL,
                point_id VARCHAR NOT NULL,
                value DOUBLE NOT NULL,
                quality INTEGER NOT NULL,
                device_id VARCHAR NOT NULL,
                data_type VARCHAR NOT NULL,
                unit VARCHAR NOT NULL,
                plant_id INTEGER NOT NULL,
                tags VARCHAR NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ 表结构创建成功")
        
        # 插入测试数据
        test_data = []
        base_time = int(datetime.now().timestamp() * 1_000_000_000)
        
        for i in range(100):
            data_types = ['TI', 'PI', 'FI', 'LI', 'AI', 'DI']
            data_type = data_types[i % len(data_types)]
            
            # 根据类型生成数值
            if data_type == 'TI':
                value = 20.0 + (i % 80)  # 温度
                unit = '°C'
            elif data_type == 'PI':
                value = i % 1000  # 压力
                unit = 'kPa'
            elif data_type == 'FI':
                value = i % 500  # 流量
                unit = 'm³/h'
            elif data_type == 'LI':
                value = (i % 100) / 10.0  # 液位
                unit = 'm'
            elif data_type == 'AI':
                value = 4.0 + (i % 16)  # 模拟量
                unit = 'mA'
            else:  # DI
                value = i % 2  # 数字量
                unit = ''
            
            tags = json.dumps({
                'plant': f'Plant_{(i % 3) + 1}',
                'unit': f'Unit_{(i % 5) + 1}',
                'type': data_type
            })
            
            test_data.append((
                i + 1,  # id
                base_time + i * 1_000_000_000,  # timestamp
                f'{data_type}_{(i % 3) + 1:02d}_{(i % 5) + 1:02d}_{i % 10:04d}',  # point_id
                value,
                192 if i % 100 < 98 else 0,  # quality
                f'PLC_{(i % 10) + 1:02d}',  # device_id
                data_type,
                unit,
                (i % 3) + 1,  # plant_id
                tags
            ))
        
        # 批量插入
        conn.executemany("""
            INSERT INTO nclink_data 
            (id, timestamp, point_id, value, quality, device_id, data_type, unit, plant_id, tags)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, test_data)
        
        print(f"✅ 插入测试数据成功: {len(test_data)} 条记录")
        
        # 验证数据
        count = conn.execute("SELECT COUNT(*) FROM nclink_data").fetchone()[0]
        print(f"✅ 数据验证成功: {count} 条记录")
        
        # 基本查询测试
        result = conn.execute("SELECT * FROM nclink_data LIMIT 5").fetchall()
        print(f"✅ 基本查询成功: 前5条记录")
        for i, row in enumerate(result, 1):
            print(f"   {i}. ID={row[0]}, Point={row[2]}, Value={row[3]:.2f}, Type={row[6]}")
        
        # 聚合查询测试
        agg_result = conn.execute("""
            SELECT data_type, COUNT(*) as count, AVG(value) as avg_value
            FROM nclink_data 
            GROUP BY data_type 
            ORDER BY data_type
        """).fetchall()
        
        print(f"✅ 聚合查询成功:")
        for row in agg_result:
            print(f"   {row[0]}: {row[1]} 条, 平均值 {row[2]:.2f}")
        
        # 窗口函数测试
        window_result = conn.execute("""
            SELECT point_id, value,
                   LAG(value) OVER (PARTITION BY point_id ORDER BY timestamp) as prev_value
            FROM nclink_data 
            WHERE data_type = 'TI'
            ORDER BY point_id, timestamp
            LIMIT 5
        """).fetchall()
        
        print(f"✅ 窗口函数查询成功:")
        for row in window_result:
            prev_val = row[2] if row[2] is not None else "None"
            print(f"   {row[0]}: 当前={row[1]:.1f}, 前值={prev_val}")
        
        conn.close()
        print("✅ 内存数据库测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 内存数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pandas_integration():
    """测试Pandas集成"""
    print("\n🧪 测试Pandas集成...")
    
    try:
        import pandas as pd
        
        # 创建内存数据库
        conn = duckdb.connect(':memory:nc-link-v2-pandas')
        
        # 创建测试数据
        conn.execute("""
            CREATE TABLE test_data AS 
            SELECT 
                i as id,
                'TI_' || (i % 3 + 1) || '_' || (i % 5 + 1) || '_' || LPAD(i::VARCHAR, 4, '0') as point_id,
                20.0 + (i % 80) as temperature,
                CASE WHEN i % 100 < 98 THEN 192 ELSE 0 END as quality,
                'PLC_' || LPAD((i % 10 + 1)::VARCHAR, 2, '0') as device_id
            FROM range(1000) t(i)
        """)
        
        # 转换为DataFrame
        df = conn.execute("SELECT * FROM test_data").df()
        print(f"✅ DataFrame转换成功: {df.shape}")
        
        # 显示基本信息
        print(f"   列名: {list(df.columns)}")
        print(f"   温度统计: 最小={df['temperature'].min():.1f}, 最大={df['temperature'].max():.1f}, 平均={df['temperature'].mean():.1f}")
        
        # 分组统计
        device_stats = df.groupby('device_id')['temperature'].agg(['count', 'mean']).round(2)
        print(f"✅ 按设备分组统计 (前5个):")
        print(device_stats.head())
        
        # 质量分析
        quality_rate = (df['quality'] == 192).mean() * 100
        print(f"✅ 数据质量率: {quality_rate:.1f}%")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Pandas集成测试失败: {e}")
        return False

def test_advanced_sql():
    """测试高级SQL功能"""
    print("\n🧪 测试高级SQL功能...")
    
    try:
        conn = duckdb.connect(':memory:nc-link-v2-advanced')
        
        # 创建时间序列数据
        conn.execute("""
            CREATE TABLE timeseries_data AS 
            SELECT 
                i as id,
                '2024-01-01 00:00:00'::TIMESTAMP + INTERVAL (i) MINUTE as timestamp,
                'TI_001' as point_id,
                20.0 + 10 * SIN(i * 0.1) + random() * 2 as temperature
            FROM range(1440) t(i)  -- 24小时数据，每分钟一条
        """)
        
        # 时间窗口分析
        hourly_stats = conn.execute("""
            SELECT 
                DATE_TRUNC('hour', timestamp) as hour,
                COUNT(*) as record_count,
                AVG(temperature) as avg_temp,
                MIN(temperature) as min_temp,
                MAX(temperature) as max_temp,
                STDDEV(temperature) as std_temp
            FROM timeseries_data
            GROUP BY hour
            ORDER BY hour
            LIMIT 5
        """).fetchall()
        
        print(f"✅ 时间窗口分析成功 (前5小时):")
        for row in hourly_stats:
            print(f"   {row[0]}: 记录数={row[1]}, 平均温度={row[2]:.2f}°C, 标准差={row[5]:.2f}")
        
        # 移动平均
        moving_avg = conn.execute("""
            SELECT 
                timestamp,
                temperature,
                AVG(temperature) OVER (
                    ORDER BY timestamp 
                    ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
                ) as moving_avg_5min
            FROM timeseries_data
            ORDER BY timestamp
            LIMIT 10
        """).fetchall()
        
        print(f"✅ 移动平均计算成功 (前10条):")
        for row in moving_avg[:5]:
            print(f"   {row[0]}: 温度={row[1]:.2f}°C, 5分钟移动平均={row[2]:.2f}°C")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 高级SQL测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🦆 内存DuckDB数据库功能测试")
    print("=" * 60)
    
    tests = [
        ("内存数据库基本功能", test_memory_database),
        ("Pandas集成", test_pandas_integration),
        ("高级SQL功能", test_advanced_sql)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试总结:")
    print(f"   总测试数: {total}")
    print(f"   通过测试: {passed}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！内存DuckDB功能正常")
        print("\n💡 下一步:")
        print("   • 运行: python nclink_v2_duckdb.py")
        print("   • 体验从二进制文件加载到内存DuckDB")
    else:
        print(f"\n⚠️ {total-passed} 个测试失败")

if __name__ == "__main__":
    main()
