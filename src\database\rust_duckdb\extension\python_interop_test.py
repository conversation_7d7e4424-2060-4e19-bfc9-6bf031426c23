#!/usr/bin/env python3
"""
数据库Python互操作性测试
测试Rust生成的数据库文件在Python环境中的兼容性
"""

import os
import sys
import json
import csv
import struct
import pandas as pd
import numpy as np
from pathlib import Path
import sqlite3
from typing import List, Dict, Any, Tuple
import time

class DatabaseInteropTester:
    """数据库互操作性测试器"""
    
    def __init__(self, test_dir: str):
        self.test_dir = Path(test_dir)
        self.results = {}
        
    def test_binary_format_reading(self) -> bool:
        """测试读取Rust生成的二进制格式"""
        print("🔧 测试二进制格式读取...")
        
        try:
            # 测试小端序格式
            le_file = self.test_dir / "test_binary_le.db"
            if le_file.exists():
                records = self._read_binary_file(le_file, 'little')
                print(f"✅ 小端序二进制文件读取成功: {len(records)} 条记录")
                
                # 验证数据完整性
                if len(records) > 0:
                    first_record = records[0]
                    print(f"   首条记录: ID={first_record['id']}, Value={first_record['value']:.2f}")
                
                self.results['binary_le'] = {
                    'status': 'success',
                    'records': len(records),
                    'file_size_mb': le_file.stat().st_size / (1024 * 1024)
                }
            
            # 测试大端序格式
            be_file = self.test_dir / "test_binary_be.db"
            if be_file.exists():
                records = self._read_binary_file(be_file, 'big')
                print(f"✅ 大端序二进制文件读取成功: {len(records)} 条记录")
                
                self.results['binary_be'] = {
                    'status': 'success', 
                    'records': len(records),
                    'file_size_mb': be_file.stat().st_size / (1024 * 1024)
                }
            
            return True
            
        except Exception as e:
            print(f"❌ 二进制格式读取失败: {e}")
            self.results['binary'] = {'status': 'failed', 'error': str(e)}
            return False
    
    def _read_binary_file(self, file_path: Path, endian: str) -> List[Dict]:
        """读取二进制数据库文件"""
        records = []
        endian_char = '<' if endian == 'little' else '>'
        
        with open(file_path, 'rb') as f:
            # 读取文件头
            magic = f.read(5)
            if magic != b'NLKDB':
                raise ValueError(f"无效的魔数: {magic}")
            
            version = struct.unpack('B', f.read(1))[0]
            endian_flag = struct.unpack('B', f.read(1))[0]
            record_count = struct.unpack(f'{endian_char}Q', f.read(8))[0]
            
            print(f"   文件版本: {version}, 字节序: {endian}, 记录数: {record_count}")
            
            # 读取记录 (只读取前100条以节省时间)
            max_records = min(100, record_count)
            for i in range(max_records):
                try:
                    record = self._read_single_record(f, endian_char)
                    records.append(record)
                except Exception as e:
                    print(f"   警告: 读取记录 {i} 失败: {e}")
                    break
        
        return records
    
    def _read_single_record(self, f, endian_char: str) -> Dict:
        """读取单条记录"""
        # 读取固定字段
        id_val = struct.unpack(f'{endian_char}Q', f.read(8))[0]
        timestamp = struct.unpack(f'{endian_char}Q', f.read(8))[0]
        value = struct.unpack(f'{endian_char}d', f.read(8))[0]
        quality = struct.unpack(f'{endian_char}I', f.read(4))[0]
        
        # 读取字符串字段
        point_id = self._read_string(f, endian_char)
        device_id = self._read_string(f, endian_char)
        data_type = self._read_string(f, endian_char)
        unit = self._read_string(f, endian_char)
        tags_json = self._read_string(f, endian_char)
        attrs_json = self._read_string(f, endian_char)
        
        # 解析JSON字段
        try:
            tags = json.loads(tags_json)
            attributes = json.loads(attrs_json)
        except json.JSONDecodeError:
            tags = {}
            attributes = {}
        
        return {
            'id': id_val,
            'timestamp': timestamp,
            'value': value,
            'quality': quality,
            'point_id': point_id,
            'device_id': device_id,
            'data_type': data_type,
            'unit': unit,
            'tags': tags,
            'attributes': attributes
        }
    
    def _read_string(self, f, endian_char: str) -> str:
        """读取字符串"""
        length = struct.unpack(f'{endian_char}I', f.read(4))[0]
        if length > 10000:  # 安全检查
            raise ValueError(f"字符串长度异常: {length}")
        return f.read(length).decode('utf-8')
    
    def test_json_format_reading(self) -> bool:
        """测试JSON格式读取"""
        print("🔧 测试JSON格式读取...")
        
        try:
            json_file = self.test_dir / "test_data.json"
            if not json_file.exists():
                print("⚠️ JSON文件不存在，跳过测试")
                return True
            
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"✅ JSON文件读取成功: {len(data)} 条记录")
            
            # 验证数据结构
            if len(data) > 0:
                first_record = data[0]
                required_fields = ['id', 'timestamp', 'value', 'quality', 'point_id']
                missing_fields = [field for field in required_fields if field not in first_record]
                
                if missing_fields:
                    print(f"⚠️ 缺少字段: {missing_fields}")
                else:
                    print("✅ JSON数据结构验证通过")
            
            self.results['json'] = {
                'status': 'success',
                'records': len(data),
                'file_size_mb': json_file.stat().st_size / (1024 * 1024)
            }
            
            return True
            
        except Exception as e:
            print(f"❌ JSON格式读取失败: {e}")
            self.results['json'] = {'status': 'failed', 'error': str(e)}
            return False
    
    def test_csv_format_reading(self) -> bool:
        """测试CSV格式读取"""
        print("🔧 测试CSV格式读取...")
        
        try:
            csv_file = self.test_dir / "test_data.csv"
            if not csv_file.exists():
                print("⚠️ CSV文件不存在，跳过测试")
                return True
            
            # 使用pandas读取CSV
            df = pd.read_csv(csv_file)
            print(f"✅ CSV文件读取成功: {len(df)} 条记录, {len(df.columns)} 列")
            
            # 验证数据类型
            print("📊 数据类型分析:")
            for col in df.columns:
                dtype = df[col].dtype
                print(f"   {col}: {dtype}")
            
            # 基本统计
            if 'value' in df.columns:
                value_stats = df['value'].describe()
                print(f"📈 数值统计: 均值={value_stats['mean']:.2f}, 标准差={value_stats['std']:.2f}")
            
            self.results['csv'] = {
                'status': 'success',
                'records': len(df),
                'columns': len(df.columns),
                'file_size_mb': csv_file.stat().st_size / (1024 * 1024)
            }
            
            return True
            
        except Exception as e:
            print(f"❌ CSV格式读取失败: {e}")
            self.results['csv'] = {'status': 'failed', 'error': str(e)}
            return False
    
    def test_pandas_integration(self) -> bool:
        """测试Pandas集成"""
        print("🔧 测试Pandas数据分析集成...")
        
        try:
            # 尝试从JSON文件创建DataFrame
            json_file = self.test_dir / "test_data.json"
            if json_file.exists():
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                # 展平嵌套结构
                flattened_data = []
                for record in data[:1000]:  # 只处理前1000条
                    flat_record = {
                        'id': record['id'],
                        'timestamp': record['timestamp'],
                        'value': record['value'],
                        'quality': record['quality'],
                        'point_id': record['point_id'],
                        'device_id': record['device_id'],
                        'data_type': record['data_type'],
                        'unit': record['unit']
                    }
                    
                    # 添加标签字段
                    if 'tags' in record:
                        for key, val in record['tags'].items():
                            flat_record[f'tag_{key}'] = val
                    
                    flattened_data.append(flat_record)
                
                df = pd.DataFrame(flattened_data)
                
                # 数据分析示例
                print(f"✅ Pandas DataFrame创建成功: {df.shape}")
                
                # 时间序列分析
                df['datetime'] = pd.to_datetime(df['timestamp'], unit='ns')
                df.set_index('datetime', inplace=True)
                
                # 按设备分组统计
                device_stats = df.groupby('device_id')['value'].agg(['count', 'mean', 'std'])
                print("📊 按设备统计:")
                print(device_stats.head())
                
                # 按数据类型分组
                type_stats = df.groupby('data_type')['value'].agg(['count', 'mean'])
                print("📊 按数据类型统计:")
                print(type_stats)
                
                self.results['pandas'] = {
                    'status': 'success',
                    'dataframe_shape': df.shape,
                    'memory_usage_mb': df.memory_usage(deep=True).sum() / (1024 * 1024)
                }
                
                return True
            else:
                print("⚠️ 没有可用的JSON文件进行Pandas测试")
                return True
                
        except Exception as e:
            print(f"❌ Pandas集成测试失败: {e}")
            self.results['pandas'] = {'status': 'failed', 'error': str(e)}
            return False
    
    def test_numpy_integration(self) -> bool:
        """测试NumPy数值计算集成"""
        print("🔧 测试NumPy数值计算集成...")
        
        try:
            json_file = self.test_dir / "test_data.json"
            if json_file.exists():
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                # 提取数值数据
                values = np.array([record['value'] for record in data[:1000]])
                timestamps = np.array([record['timestamp'] for record in data[:1000]])
                
                # 数值分析
                print(f"✅ NumPy数组创建成功: {values.shape}")
                print(f"📊 数值统计:")
                print(f"   均值: {np.mean(values):.4f}")
                print(f"   标准差: {np.std(values):.4f}")
                print(f"   最小值: {np.min(values):.4f}")
                print(f"   最大值: {np.max(values):.4f}")
                
                # 时间序列分析
                time_diffs = np.diff(timestamps)
                avg_interval = np.mean(time_diffs)
                print(f"⏱️ 平均采样间隔: {avg_interval/1e9:.6f} 秒")
                
                # 简单的信号处理
                if len(values) > 10:
                    # 移动平均
                    window_size = 10
                    moving_avg = np.convolve(values, np.ones(window_size)/window_size, mode='valid')
                    print(f"📈 移动平均计算完成: {len(moving_avg)} 个点")
                
                self.results['numpy'] = {
                    'status': 'success',
                    'array_size': len(values),
                    'mean_value': float(np.mean(values)),
                    'std_value': float(np.std(values))
                }
                
                return True
            else:
                print("⚠️ 没有可用的数据文件进行NumPy测试")
                return True
                
        except Exception as e:
            print(f"❌ NumPy集成测试失败: {e}")
            self.results['numpy'] = {'status': 'failed', 'error': str(e)}
            return False
    
    def test_performance_benchmarks(self) -> bool:
        """测试性能基准"""
        print("🔧 测试Python读取性能...")
        
        try:
            # 测试JSON读取性能
            json_file = self.test_dir / "test_data.json"
            if json_file.exists():
                start_time = time.time()
                with open(json_file, 'r') as f:
                    data = json.load(f)
                json_time = time.time() - start_time
                
                print(f"📊 JSON读取性能: {len(data)} 条记录, {json_time:.3f} 秒")
                print(f"   吞吐量: {len(data)/json_time:.0f} 条/秒")
            
            # 测试CSV读取性能
            csv_file = self.test_dir / "test_data.csv"
            if csv_file.exists():
                start_time = time.time()
                df = pd.read_csv(csv_file)
                csv_time = time.time() - start_time
                
                print(f"📊 CSV读取性能: {len(df)} 条记录, {csv_time:.3f} 秒")
                print(f"   吞吐量: {len(df)/csv_time:.0f} 条/秒")
            
            self.results['performance'] = {
                'status': 'success',
                'json_read_time': json_time if 'json_time' in locals() else None,
                'csv_read_time': csv_time if 'csv_time' in locals() else None
            }
            
            return True
            
        except Exception as e:
            print(f"❌ 性能测试失败: {e}")
            self.results['performance'] = {'status': 'failed', 'error': str(e)}
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有互操作性测试"""
        print("🐍 Python互操作性测试套件")
        print("=" * 60)
        
        print(f"📂 测试目录: {self.test_dir}")
        
        # 检查测试文件是否存在
        test_files = [
            "test_binary_le.db",
            "test_binary_be.db", 
            "test_data.json",
            "test_data.csv"
        ]
        
        existing_files = []
        for file_name in test_files:
            file_path = self.test_dir / file_name
            if file_path.exists():
                existing_files.append(file_name)
                size_mb = file_path.stat().st_size / (1024 * 1024)
                print(f"✅ 发现测试文件: {file_name} ({size_mb:.2f} MB)")
            else:
                print(f"⚠️ 测试文件不存在: {file_name}")
        
        if not existing_files:
            print("❌ 没有找到任何测试文件，请先运行Rust兼容性测试生成器")
            return {'status': 'no_test_files'}
        
        print("\n🧪 开始互操作性测试...")
        
        # 运行各项测试
        tests = [
            self.test_binary_format_reading,
            self.test_json_format_reading,
            self.test_csv_format_reading,
            self.test_pandas_integration,
            self.test_numpy_integration,
            self.test_performance_benchmarks
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        # 生成测试报告
        self.results['summary'] = {
            'total_tests': total,
            'passed_tests': passed,
            'success_rate': passed / total * 100
        }
        
        print(f"\n📈 测试总结:")
        print(f"   总测试数: {total}")
        print(f"   通过测试: {passed}")
        print(f"   成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("🎉 所有Python互操作性测试通过!")
        else:
            print(f"⚠️ {total-passed} 个测试失败")
        
        return self.results

def main():
    """主函数"""
    test_dir = r"D:\database\compatibility_test"
    
    if not os.path.exists(test_dir):
        print(f"❌ 测试目录不存在: {test_dir}")
        print("请先运行Rust兼容性测试生成器")
        sys.exit(1)
    
    tester = DatabaseInteropTester(test_dir)
    results = tester.run_all_tests()
    
    # 保存测试结果
    results_file = Path(test_dir) / "python_interop_results.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 测试结果已保存到: {results_file}")

if __name__ == "__main__":
    main()
