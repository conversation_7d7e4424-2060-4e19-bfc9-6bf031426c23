#!/usr/bin/env python3
"""
nclink_v2数据库Python CRUD操作脚本
支持对Rust生成的nclink_v2.duckdb数据库进行完整的增删查改操作
"""

import os
import sys
import struct
import json
import sqlite3
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import pandas as pd
import numpy as np

class NclinkV2Database:
    """nclink_v2数据库操作类"""
    
    def __init__(self, db_path: str, wal_path: str = None):
        """
        初始化数据库连接
        
        Args:
            db_path: 数据库文件路径
            wal_path: WAL文件路径 (可选)
        """
        self.db_path = Path(db_path)
        self.wal_path = Path(wal_path) if wal_path else self.db_path.with_suffix('.duckdb.wal')
        
        if not self.db_path.exists():
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
        
        # 创建SQLite内存数据库用于操作
        self.conn = sqlite3.connect(':memory:')
        self.conn.row_factory = sqlite3.Row
        
        # 创建表结构
        self._create_tables()
        
        # 加载数据
        self._load_data()
        
        print(f"✅ 成功连接到数据库: {self.db_path.name}")
        print(f"📊 数据库大小: {self.db_path.stat().st_size / (1024*1024):.2f} MB")
    
    def _create_tables(self):
        """创建数据表结构"""
        create_sql = """
        CREATE TABLE IF NOT EXISTS nclink_data (
            id INTEGER PRIMARY KEY,
            timestamp INTEGER NOT NULL,
            point_id TEXT NOT NULL,
            value REAL NOT NULL,
            quality INTEGER NOT NULL,
            device_id TEXT NOT NULL,
            data_type TEXT NOT NULL,
            unit TEXT NOT NULL,
            plant_id INTEGER NOT NULL,
            tags TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_timestamp ON nclink_data(timestamp);
        CREATE INDEX IF NOT EXISTS idx_point_id ON nclink_data(point_id);
        CREATE INDEX IF NOT EXISTS idx_device_id ON nclink_data(device_id);
        CREATE INDEX IF NOT EXISTS idx_data_type ON nclink_data(data_type);
        CREATE INDEX IF NOT EXISTS idx_plant_id ON nclink_data(plant_id);
        """
        
        self.conn.executescript(create_sql)
        self.conn.commit()
    
    def _load_data(self):
        """从二进制文件加载数据到SQLite"""
        print("📥 正在加载数据...")
        
        try:
            with open(self.db_path, 'rb') as f:
                # 读取文件头
                magic = f.read(5)
                if magic != b'NLKDB':
                    raise ValueError(f"无效的数据库文件格式: {magic}")
                
                version = struct.unpack('B', f.read(1))[0]
                endian_flag = struct.unpack('B', f.read(1))[0]
                record_count = struct.unpack('<Q', f.read(8))[0]
                
                print(f"📋 数据库信息: 版本={version}, 记录数={record_count:,}")
                
                # 读取记录
                records = []
                for i in range(min(record_count, 100000)):  # 限制加载数量
                    try:
                        record = self._read_record(f)
                        records.append(record)
                        
                        if (i + 1) % 10000 == 0:
                            print(f"   已加载: {i+1:,} 条记录")
                    except Exception as e:
                        print(f"⚠️ 读取记录 {i} 失败: {e}")
                        break
                
                # 批量插入到SQLite
                if records:
                    self._batch_insert(records)
                    print(f"✅ 成功加载 {len(records):,} 条记录")
                
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            raise
    
    def _read_record(self, f) -> Dict[str, Any]:
        """读取单条记录"""
        # 读取固定字段
        id_val = struct.unpack('<Q', f.read(8))[0]
        timestamp = struct.unpack('<Q', f.read(8))[0]
        value = struct.unpack('<d', f.read(8))[0]
        quality = struct.unpack('<I', f.read(4))[0]
        plant_id = struct.unpack('<I', f.read(4))[0]
        
        # 读取字符串字段
        point_id = self._read_string(f)
        device_id = self._read_string(f)
        data_type = self._read_string(f)
        unit = self._read_string(f)
        tags = self._read_string(f)
        
        return {
            'id': id_val,
            'timestamp': timestamp,
            'point_id': point_id,
            'value': value,
            'quality': quality,
            'device_id': device_id,
            'data_type': data_type,
            'unit': unit,
            'plant_id': plant_id,
            'tags': tags
        }
    
    def _read_string(self, f) -> str:
        """读取字符串"""
        length = struct.unpack('<I', f.read(4))[0]
        if length > 10000:  # 安全检查
            raise ValueError(f"字符串长度异常: {length}")
        return f.read(length).decode('utf-8')
    
    def _batch_insert(self, records: List[Dict[str, Any]]):
        """批量插入记录"""
        insert_sql = """
        INSERT INTO nclink_data 
        (id, timestamp, point_id, value, quality, device_id, data_type, unit, plant_id, tags)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        data = [
            (r['id'], r['timestamp'], r['point_id'], r['value'], r['quality'],
             r['device_id'], r['data_type'], r['unit'], r['plant_id'], r['tags'])
            for r in records
        ]
        
        self.conn.executemany(insert_sql, data)
        self.conn.commit()
    
    # ==================== CRUD操作 ====================
    
    def create_record(self, point_id: str, value: float, quality: int = 192,
                     device_id: str = "PLC_01", data_type: str = "AI", 
                     unit: str = "", plant_id: int = 1, tags: Dict = None) -> int:
        """
        创建新记录 (Create)
        
        Args:
            point_id: 测点ID
            value: 数值
            quality: 数据质量
            device_id: 设备ID
            data_type: 数据类型
            unit: 单位
            plant_id: 工厂ID
            tags: 标签字典
            
        Returns:
            新记录的ID
        """
        if tags is None:
            tags = {}
        
        # 生成新ID
        cursor = self.conn.execute("SELECT MAX(id) FROM nclink_data")
        max_id = cursor.fetchone()[0] or 0
        new_id = max_id + 1
        
        # 当前时间戳
        timestamp = int(datetime.now().timestamp() * 1_000_000_000)  # 纳秒
        
        # 插入记录
        insert_sql = """
        INSERT INTO nclink_data 
        (id, timestamp, point_id, value, quality, device_id, data_type, unit, plant_id, tags)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        self.conn.execute(insert_sql, (
            new_id, timestamp, point_id, value, quality,
            device_id, data_type, unit, plant_id, json.dumps(tags)
        ))
        self.conn.commit()
        
        print(f"✅ 创建记录成功: ID={new_id}, Point={point_id}, Value={value}")
        return new_id
    
    def read_records(self, limit: int = 100, offset: int = 0, 
                    filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        读取记录 (Read)
        
        Args:
            limit: 限制数量
            offset: 偏移量
            filters: 过滤条件
            
        Returns:
            记录列表
        """
        sql = "SELECT * FROM nclink_data"
        params = []
        
        # 构建WHERE条件
        if filters:
            conditions = []
            for key, value in filters.items():
                if key in ['id', 'plant_id', 'quality']:
                    conditions.append(f"{key} = ?")
                    params.append(value)
                elif key in ['point_id', 'device_id', 'data_type', 'unit']:
                    conditions.append(f"{key} LIKE ?")
                    params.append(f"%{value}%")
                elif key == 'value_range':
                    conditions.append("value BETWEEN ? AND ?")
                    params.extend(value)
                elif key == 'timestamp_range':
                    conditions.append("timestamp BETWEEN ? AND ?")
                    params.extend(value)
            
            if conditions:
                sql += " WHERE " + " AND ".join(conditions)
        
        sql += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        cursor = self.conn.execute(sql, params)
        records = [dict(row) for row in cursor.fetchall()]
        
        # 转换时间戳为可读格式
        for record in records:
            record['datetime'] = datetime.fromtimestamp(record['timestamp'] / 1_000_000_000)
            try:
                record['tags_dict'] = json.loads(record['tags'])
            except:
                record['tags_dict'] = {}
        
        return records
    
    def update_record(self, record_id: int, updates: Dict[str, Any]) -> bool:
        """
        更新记录 (Update)
        
        Args:
            record_id: 记录ID
            updates: 更新字段字典
            
        Returns:
            是否更新成功
        """
        if not updates:
            return False
        
        # 构建UPDATE语句
        set_clauses = []
        params = []
        
        for key, value in updates.items():
            if key in ['point_id', 'value', 'quality', 'device_id', 'data_type', 
                      'unit', 'plant_id', 'tags']:
                set_clauses.append(f"{key} = ?")
                params.append(value if key != 'tags' else json.dumps(value))
        
        if not set_clauses:
            return False
        
        # 添加更新时间
        set_clauses.append("updated_at = CURRENT_TIMESTAMP")
        params.append(record_id)
        
        sql = f"UPDATE nclink_data SET {', '.join(set_clauses)} WHERE id = ?"
        
        cursor = self.conn.execute(sql, params)
        self.conn.commit()
        
        success = cursor.rowcount > 0
        if success:
            print(f"✅ 更新记录成功: ID={record_id}")
        else:
            print(f"⚠️ 记录不存在: ID={record_id}")
        
        return success
    
    def delete_record(self, record_id: int) -> bool:
        """
        删除记录 (Delete)
        
        Args:
            record_id: 记录ID
            
        Returns:
            是否删除成功
        """
        cursor = self.conn.execute("DELETE FROM nclink_data WHERE id = ?", (record_id,))
        self.conn.commit()
        
        success = cursor.rowcount > 0
        if success:
            print(f"✅ 删除记录成功: ID={record_id}")
        else:
            print(f"⚠️ 记录不存在: ID={record_id}")
        
        return success
    
    def delete_records(self, filters: Dict[str, Any]) -> int:
        """
        批量删除记录
        
        Args:
            filters: 删除条件
            
        Returns:
            删除的记录数
        """
        if not filters:
            print("⚠️ 批量删除需要指定过滤条件")
            return 0
        
        # 构建WHERE条件
        conditions = []
        params = []
        
        for key, value in filters.items():
            if key in ['plant_id', 'quality']:
                conditions.append(f"{key} = ?")
                params.append(value)
            elif key in ['device_id', 'data_type']:
                conditions.append(f"{key} LIKE ?")
                params.append(f"%{value}%")
        
        if not conditions:
            print("⚠️ 无效的删除条件")
            return 0
        
        sql = f"DELETE FROM nclink_data WHERE {' AND '.join(conditions)}"
        cursor = self.conn.execute(sql, params)
        self.conn.commit()
        
        deleted_count = cursor.rowcount
        print(f"✅ 批量删除成功: {deleted_count} 条记录")
        return deleted_count
    
    # ==================== 统计和分析 ====================
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        stats = {}
        
        # 基本统计
        cursor = self.conn.execute("SELECT COUNT(*) FROM nclink_data")
        stats['total_records'] = cursor.fetchone()[0]
        
        cursor = self.conn.execute("SELECT COUNT(DISTINCT point_id) FROM nclink_data")
        stats['unique_points'] = cursor.fetchone()[0]
        
        cursor = self.conn.execute("SELECT COUNT(DISTINCT device_id) FROM nclink_data")
        stats['unique_devices'] = cursor.fetchone()[0]
        
        cursor = self.conn.execute("SELECT COUNT(DISTINCT plant_id) FROM nclink_data")
        stats['unique_plants'] = cursor.fetchone()[0]
        
        # 数据类型分布
        cursor = self.conn.execute("""
            SELECT data_type, COUNT(*) as count 
            FROM nclink_data 
            GROUP BY data_type 
            ORDER BY count DESC
        """)
        stats['data_type_distribution'] = dict(cursor.fetchall())
        
        # 数据质量统计
        cursor = self.conn.execute("""
            SELECT 
                SUM(CASE WHEN quality = 192 THEN 1 ELSE 0 END) as good_quality,
                SUM(CASE WHEN quality != 192 THEN 1 ELSE 0 END) as bad_quality
            FROM nclink_data
        """)
        quality_stats = cursor.fetchone()
        stats['quality_stats'] = {
            'good': quality_stats[0],
            'bad': quality_stats[1],
            'good_percentage': quality_stats[0] / stats['total_records'] * 100 if stats['total_records'] > 0 else 0
        }
        
        # 数值统计
        cursor = self.conn.execute("""
            SELECT 
                MIN(value) as min_value,
                MAX(value) as max_value,
                AVG(value) as avg_value,
                COUNT(*) as count
            FROM nclink_data
        """)
        value_stats = cursor.fetchone()
        stats['value_stats'] = {
            'min': value_stats[0],
            'max': value_stats[1],
            'avg': value_stats[2],
            'count': value_stats[3]
        }
        
        return stats
    
    def get_dataframe(self, filters: Dict[str, Any] = None, limit: int = 10000) -> pd.DataFrame:
        """获取Pandas DataFrame"""
        records = self.read_records(limit=limit, filters=filters)
        
        if not records:
            return pd.DataFrame()
        
        df = pd.DataFrame(records)
        
        # 转换时间戳
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ns')
        
        # 解析标签
        df['tags_dict'] = df['tags'].apply(lambda x: json.loads(x) if x else {})
        
        return df
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("✅ 数据库连接已关闭")

def main():
    """主函数 - 演示CRUD操作"""
    print("🗄️ nclink_v2数据库Python CRUD操作演示")
    print("=" * 60)
    
    # 连接数据库
    db_path = r"D:\database\nclink_v2.duckdb"
    
    try:
        db = NclinkV2Database(db_path)
        
        print("\n📊 数据库统计信息:")
        stats = db.get_statistics()
        print(f"   总记录数: {stats['total_records']:,}")
        print(f"   测点数: {stats['unique_points']:,}")
        print(f"   设备数: {stats['unique_devices']}")
        print(f"   工厂数: {stats['unique_plants']}")
        print(f"   数据质量: {stats['quality_stats']['good_percentage']:.1f}% 良好")
        
        print("\n📈 数据类型分布:")
        for data_type, count in stats['data_type_distribution'].items():
            print(f"   {data_type}: {count:,} 条")
        
        print("\n🧪 CRUD操作演示:")
        
        # Create - 创建新记录
        print("\n1️⃣ 创建操作 (Create):")
        new_id = db.create_record(
            point_id="TEST_01_01_0001",
            value=25.5,
            quality=192,
            device_id="PLC_TEST",
            data_type="TI",
            unit="°C",
            plant_id=1,
            tags={"test": True, "location": "Lab"}
        )
        
        # Read - 读取记录
        print("\n2️⃣ 读取操作 (Read):")
        records = db.read_records(limit=5)
        print(f"   最新5条记录:")
        for record in records:
            print(f"   ID={record['id']}, Point={record['point_id']}, "
                  f"Value={record['value']:.2f}, Type={record['data_type']}")
        
        # 条件查询
        print("\n   条件查询 (温度数据):")
        temp_records = db.read_records(
            limit=3,
            filters={'data_type': 'TI'}
        )
        for record in temp_records:
            print(f"   ID={record['id']}, Point={record['point_id']}, "
                  f"Value={record['value']:.2f}°C")
        
        # Update - 更新记录
        print("\n3️⃣ 更新操作 (Update):")
        if new_id:
            success = db.update_record(new_id, {
                'value': 26.8,
                'tags': {"test": True, "location": "Lab", "updated": True}
            })
        
        # Delete - 删除记录
        print("\n4️⃣ 删除操作 (Delete):")
        if new_id:
            success = db.delete_record(new_id)
        
        print("\n📊 Pandas集成演示:")
        df = db.get_dataframe(limit=1000)
        print(f"   DataFrame形状: {df.shape}")
        print(f"   数值统计:")
        print(f"   均值: {df['value'].mean():.2f}")
        print(f"   标准差: {df['value'].std():.2f}")
        
        # 按数据类型分组
        type_stats = df.groupby('data_type')['value'].agg(['count', 'mean', 'std'])
        print(f"\n   按数据类型统计:")
        print(type_stats)
        
        db.close()
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()

def interactive_demo():
    """交互式演示"""
    print("🎮 nclink_v2数据库交互式操作")
    print("=" * 50)

    db_path = r"D:\database\nclink_v2.duckdb"

    try:
        db = NclinkV2Database(db_path)

        while True:
            print("\n📋 操作菜单:")
            print("1. 查看统计信息")
            print("2. 查询记录")
            print("3. 创建记录")
            print("4. 更新记录")
            print("5. 删除记录")
            print("6. 数据分析")
            print("7. 导出数据")
            print("0. 退出")

            choice = input("\n请选择操作 (0-7): ").strip()

            if choice == '0':
                break
            elif choice == '1':
                show_statistics(db)
            elif choice == '2':
                query_records(db)
            elif choice == '3':
                create_record_interactive(db)
            elif choice == '4':
                update_record_interactive(db)
            elif choice == '5':
                delete_record_interactive(db)
            elif choice == '6':
                data_analysis(db)
            elif choice == '7':
                export_data(db)
            else:
                print("❌ 无效选择，请重试")

        db.close()

    except Exception as e:
        print(f"❌ 操作失败: {e}")

def show_statistics(db):
    """显示统计信息"""
    stats = db.get_statistics()
    print(f"\n📊 数据库统计:")
    print(f"   总记录数: {stats['total_records']:,}")
    print(f"   测点数: {stats['unique_points']:,}")
    print(f"   设备数: {stats['unique_devices']}")
    print(f"   工厂数: {stats['unique_plants']}")
    print(f"   数据质量: {stats['quality_stats']['good_percentage']:.1f}% 良好")

    print(f"\n📈 数据类型分布:")
    for data_type, count in stats['data_type_distribution'].items():
        percentage = count / stats['total_records'] * 100
        print(f"   {data_type}: {count:,} 条 ({percentage:.1f}%)")

def query_records(db):
    """查询记录"""
    print("\n🔍 查询记录:")
    print("1. 查看最新记录")
    print("2. 按设备查询")
    print("3. 按数据类型查询")
    print("4. 按数值范围查询")

    choice = input("请选择查询方式 (1-4): ").strip()

    if choice == '1':
        limit = int(input("显示记录数 (默认10): ") or "10")
        records = db.read_records(limit=limit)
        display_records(records)

    elif choice == '2':
        device_id = input("设备ID (如 PLC_01): ").strip()
        records = db.read_records(limit=20, filters={'device_id': device_id})
        display_records(records)

    elif choice == '3':
        data_type = input("数据类型 (TI/PI/FI/LI/AI/DI): ").strip().upper()
        records = db.read_records(limit=20, filters={'data_type': data_type})
        display_records(records)

    elif choice == '4':
        min_val = float(input("最小值: "))
        max_val = float(input("最大值: "))
        records = db.read_records(limit=20, filters={'value_range': [min_val, max_val]})
        display_records(records)

def display_records(records):
    """显示记录"""
    if not records:
        print("📭 没有找到匹配的记录")
        return

    print(f"\n📋 查询结果 ({len(records)} 条):")
    print("-" * 80)
    for record in records[:10]:  # 只显示前10条
        print(f"ID: {record['id']:6} | Point: {record['point_id']:15} | "
              f"Value: {record['value']:8.2f} | Type: {record['data_type']:2} | "
              f"Device: {record['device_id']:8}")

def create_record_interactive(db):
    """交互式创建记录"""
    print("\n➕ 创建新记录:")

    try:
        point_id = input("测点ID: ").strip()
        value = float(input("数值: "))
        data_type = input("数据类型 (TI/PI/FI/LI/AI/DI): ").strip().upper()
        device_id = input("设备ID (默认 PLC_01): ").strip() or "PLC_01"
        unit = input("单位 (可选): ").strip()
        plant_id = int(input("工厂ID (默认 1): ") or "1")

        new_id = db.create_record(
            point_id=point_id,
            value=value,
            data_type=data_type,
            device_id=device_id,
            unit=unit,
            plant_id=plant_id,
            tags={"manual_entry": True}
        )

        print(f"✅ 记录创建成功，ID: {new_id}")

    except ValueError as e:
        print(f"❌ 输入错误: {e}")
    except Exception as e:
        print(f"❌ 创建失败: {e}")

def update_record_interactive(db):
    """交互式更新记录"""
    print("\n✏️ 更新记录:")

    try:
        record_id = int(input("记录ID: "))

        # 显示当前记录
        records = db.read_records(filters={'id': record_id})
        if not records:
            print("❌ 记录不存在")
            return

        record = records[0]
        print(f"当前记录: Point={record['point_id']}, Value={record['value']}")

        updates = {}

        new_value = input(f"新数值 (当前: {record['value']}): ").strip()
        if new_value:
            updates['value'] = float(new_value)

        new_quality = input(f"新质量 (当前: {record['quality']}): ").strip()
        if new_quality:
            updates['quality'] = int(new_quality)

        if updates:
            success = db.update_record(record_id, updates)
            if success:
                print("✅ 更新成功")
        else:
            print("ℹ️ 没有更新任何字段")

    except ValueError as e:
        print(f"❌ 输入错误: {e}")
    except Exception as e:
        print(f"❌ 更新失败: {e}")

def delete_record_interactive(db):
    """交互式删除记录"""
    print("\n🗑️ 删除记录:")

    try:
        record_id = int(input("记录ID: "))

        # 确认删除
        confirm = input(f"确认删除记录 {record_id}? (y/N): ").strip().lower()
        if confirm == 'y':
            success = db.delete_record(record_id)
            if success:
                print("✅ 删除成功")
        else:
            print("ℹ️ 取消删除")

    except ValueError as e:
        print(f"❌ 输入错误: {e}")
    except Exception as e:
        print(f"❌ 删除失败: {e}")

def data_analysis(db):
    """数据分析"""
    print("\n📊 数据分析:")

    df = db.get_dataframe(limit=10000)

    if df.empty:
        print("📭 没有数据可分析")
        return

    print(f"数据集大小: {df.shape[0]:,} 行 × {df.shape[1]} 列")

    # 数值统计
    print(f"\n📈 数值统计:")
    print(f"   最小值: {df['value'].min():.2f}")
    print(f"   最大值: {df['value'].max():.2f}")
    print(f"   平均值: {df['value'].mean():.2f}")
    print(f"   中位数: {df['value'].median():.2f}")
    print(f"   标准差: {df['value'].std():.2f}")

    # 按设备统计
    print(f"\n🏭 按设备统计:")
    device_stats = df.groupby('device_id')['value'].agg(['count', 'mean']).round(2)
    print(device_stats)

    # 按数据类型统计
    print(f"\n📋 按数据类型统计:")
    type_stats = df.groupby('data_type')['value'].agg(['count', 'mean', 'std']).round(2)
    print(type_stats)

def export_data(db):
    """导出数据"""
    print("\n💾 导出数据:")
    print("1. 导出为CSV")
    print("2. 导出为JSON")
    print("3. 导出为Excel")

    choice = input("请选择导出格式 (1-3): ").strip()

    try:
        limit = int(input("导出记录数 (默认1000): ") or "1000")
        df = db.get_dataframe(limit=limit)

        if df.empty:
            print("📭 没有数据可导出")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if choice == '1':
            filename = f"nclink_v2_export_{timestamp}.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✅ 已导出到: {filename}")

        elif choice == '2':
            filename = f"nclink_v2_export_{timestamp}.json"
            df.to_json(filename, orient='records', indent=2, force_ascii=False)
            print(f"✅ 已导出到: {filename}")

        elif choice == '3':
            filename = f"nclink_v2_export_{timestamp}.xlsx"
            df.to_excel(filename, index=False)
            print(f"✅ 已导出到: {filename}")

    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == '--interactive':
        interactive_demo()
    else:
        main()
