using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.IO;
using System.Text;

/// <summary>
/// C# DuckDB文件读取器
/// 读取nclink_v2.duckdb和nclink_v2.duckdb.wal文件
/// </summary>
public class DuckDBReader : IDisposable
{
    private SQLiteConnection _connection;
    private readonly string _dbPath;

    public DuckDBReader(string dbPath)
    {
        _dbPath = dbPath;
    }

    /// <summary>
    /// 连接数据库
    /// </summary>
    public bool Connect()
    {
        try
        {
            var connectionString = $"Data Source={_dbPath};Version=3;";
            _connection = new SQLiteConnection(connectionString);
            _connection.Open();
            Console.WriteLine("✅ 连接成功");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 连接失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 读取DuckDB文件头信息
    /// </summary>
    public DuckDBHeader ReadHeader()
    {
        try
        {
            using var fs = new FileStream(_dbPath, FileMode.Open, FileAccess.Read);
            using var reader = new BinaryReader(fs);

            var magic = reader.ReadBytes(8);
            var version = reader.ReadUInt32();
            var pageSize = reader.ReadUInt32();
            var compressionType = reader.ReadUInt32();
            var createdAt = reader.ReadUInt64();
            var modifiedAt = reader.ReadUInt64();
            var recordCount = reader.ReadUInt64();
            var compressionRatio = reader.ReadDouble();

            var compressionTypes = new[] { "None", "LZ4", "Gzip", "Snappy", "ZSTD" };

            return new DuckDBHeader
            {
                Version = version,
                PageSize = pageSize,
                CompressionType = compressionType < compressionTypes.Length ? 
                    compressionTypes[compressionType] : "Unknown",
                CreatedAt = DateTimeOffset.FromUnixTimeSeconds((long)createdAt).DateTime,
                RecordCount = recordCount,
                CompressionRatio = compressionRatio
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"读取文件头失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 执行SQL查询
    /// </summary>
    public List<Dictionary<string, object>> Query(string sql, params object[] parameters)
    {
        if (_connection == null)
            throw new InvalidOperationException("未连接数据库");

        var results = new List<Dictionary<string, object>>();

        using var command = new SQLiteCommand(sql, _connection);
        
        // 添加参数
        for (int i = 0; i < parameters.Length; i++)
        {
            command.Parameters.AddWithValue($"@p{i}", parameters[i]);
        }

        using var reader = command.ExecuteReader();
        
        while (reader.Read())
        {
            var row = new Dictionary<string, object>();
            for (int i = 0; i < reader.FieldCount; i++)
            {
                row[reader.GetName(i)] = reader.GetValue(i);
            }
            results.Add(row);
        }

        return results;
    }

    /// <summary>
    /// 获取所有数据
    /// </summary>
    public List<EngineeringData> GetAllData(int limit = 1000)
    {
        var sql = $"SELECT * FROM engineering_data LIMIT {limit}";
        var results = Query(sql);
        
        return results.ConvertAll(row => new EngineeringData
        {
            PointId = row["point_id"].ToString(),
            Timestamp = Convert.ToInt64(row["timestamp"]),
            Value = Convert.ToDouble(row["value"]),
            Quality = Convert.ToInt32(row["quality"]),
            DeviceId = row["device_id"].ToString(),
            Tags = row["tags"].ToString()
        });
    }

    /// <summary>
    /// 按设备查询
    /// </summary>
    public List<EngineeringData> GetByDevice(string deviceId, int limit = 100)
    {
        var sql = "SELECT * FROM engineering_data WHERE device_id = @p0 ORDER BY timestamp DESC LIMIT @p1";
        var results = Query(sql, deviceId, limit);
        
        return results.ConvertAll(row => new EngineeringData
        {
            PointId = row["point_id"].ToString(),
            Timestamp = Convert.ToInt64(row["timestamp"]),
            Value = Convert.ToDouble(row["value"]),
            Quality = Convert.ToInt32(row["quality"]),
            DeviceId = row["device_id"].ToString(),
            Tags = row["tags"].ToString()
        });
    }

    /// <summary>
    /// 按测点类型查询
    /// </summary>
    public List<EngineeringData> GetByPointType(string pointType, int limit = 100)
    {
        var sql = $"SELECT * FROM engineering_data WHERE point_id LIKE '%{pointType}%' ORDER BY timestamp DESC LIMIT @p0";
        var results = Query(sql, limit);
        
        return results.ConvertAll(row => new EngineeringData
        {
            PointId = row["point_id"].ToString(),
            Timestamp = Convert.ToInt64(row["timestamp"]),
            Value = Convert.ToDouble(row["value"]),
            Quality = Convert.ToInt32(row["quality"]),
            DeviceId = row["device_id"].ToString(),
            Tags = row["tags"].ToString()
        });
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    public DatabaseStatistics GetStatistics()
    {
        var sql = @"
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT point_id) as unique_points,
                COUNT(DISTINCT device_id) as unique_devices,
                MIN(timestamp) as start_time,
                MAX(timestamp) as end_time,
                AVG(value) as avg_value
            FROM engineering_data";
        
        var result = Query(sql)[0];
        
        return new DatabaseStatistics
        {
            TotalRecords = Convert.ToInt64(result["total_records"]),
            UniquePoints = Convert.ToInt64(result["unique_points"]),
            UniqueDevices = Convert.ToInt64(result["unique_devices"]),
            StartTime = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(result["start_time"])).DateTime,
            EndTime = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(result["end_time"])).DateTime,
            AverageValue = Convert.ToDouble(result["avg_value"])
        };
    }

    public void Dispose()
    {
        _connection?.Close();
        _connection?.Dispose();
        Console.WriteLine("🔒 数据库已关闭");
    }
}

/// <summary>
/// DuckDB文件头信息
/// </summary>
public class DuckDBHeader
{
    public uint Version { get; set; }
    public uint PageSize { get; set; }
    public string CompressionType { get; set; }
    public DateTime CreatedAt { get; set; }
    public ulong RecordCount { get; set; }
    public double CompressionRatio { get; set; }
}

/// <summary>
/// 工程数据
/// </summary>
public class EngineeringData
{
    public string PointId { get; set; }
    public long Timestamp { get; set; }
    public double Value { get; set; }
    public int Quality { get; set; }
    public string DeviceId { get; set; }
    public string Tags { get; set; }
    
    public DateTime DateTime => DateTimeOffset.FromUnixTimeSeconds(Timestamp).DateTime;
}

/// <summary>
/// 数据库统计信息
/// </summary>
public class DatabaseStatistics
{
    public long TotalRecords { get; set; }
    public long UniquePoints { get; set; }
    public long UniqueDevices { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public double AverageValue { get; set; }
}

/// <summary>
/// 使用示例
/// </summary>
class Program
{
    static void Main(string[] args)
    {
        using var reader = new DuckDBReader("engineering_data.db"); // 或 "nclink_v2.duckdb"
        
        if (reader.Connect())
        {
            // 读取文件头
            var header = reader.ReadHeader();
            if (header != null)
            {
                Console.WriteLine($"📊 记录数: {header.RecordCount:N0}");
                Console.WriteLine($"🗜️ 压缩比: {header.CompressionRatio:F1}:1");
                Console.WriteLine($"📅 创建时间: {header.CreatedAt}");
            }

            // 获取统计信息
            var stats = reader.GetStatistics();
            Console.WriteLine($"📈 总记录: {stats.TotalRecords:N0}");
            Console.WriteLine($"🔧 测点数: {stats.UniquePoints:N0}");
            Console.WriteLine($"🖥️ 设备数: {stats.UniqueDevices:N0}");

            // 查询最新数据
            var recentData = reader.GetAllData(5);
            Console.WriteLine("\n📋 最新5条记录:");
            foreach (var record in recentData)
            {
                Console.WriteLine($"  {record.PointId}: {record.Value:F2} @ {record.DateTime}");
            }

            // 按设备查询
            var deviceData = reader.GetByDevice("Plant_A_Unit_01_Reactor_PLC", 3);
            Console.WriteLine($"\n🖥️ 设备数据 ({deviceData.Count} 条):");
            foreach (var record in deviceData)
            {
                Console.WriteLine($"  {record.PointId}: {record.Value:F2}");
            }
        }
    }
}

/*
NuGet包依赖:
Install-Package System.Data.SQLite

使用方法:
using var reader = new DuckDBReader("nclink_v2.duckdb");
if (reader.Connect())
{
    var data = reader.GetAllData(100);
    var stats = reader.GetStatistics();
}
*/
