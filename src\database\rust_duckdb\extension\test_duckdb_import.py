#!/usr/bin/env python3
"""
测试DuckDB导入和基本功能
"""

def test_duckdb_import():
    """测试DuckDB导入"""
    print("🧪 测试DuckDB导入...")
    
    try:
        import duckdb
        print(f"✅ DuckDB导入成功，版本: {duckdb.__version__}")
        return True
    except ImportError as e:
        print(f"❌ DuckDB导入失败: {e}")
        print("💡 请运行: pip install duckdb")
        return False

def test_duckdb_basic_functionality():
    """测试DuckDB基本功能"""
    print("\n🧪 测试DuckDB基本功能...")
    
    try:
        import duckdb
        
        # 创建内存连接
        conn = duckdb.connect(':memory:')
        print("✅ 内存连接创建成功")
        
        # 创建测试表
        conn.execute("""
            CREATE TABLE test (
                id INTEGER,
                name VARCHAR,
                value DOUBLE
            )
        """)
        print("✅ 表创建成功")
        
        # 插入数据
        conn.execute("INSERT INTO test VALUES (1, 'test1', 10.5)")
        conn.execute("INSERT INTO test VALUES (2, 'test2', 20.3)")
        print("✅ 数据插入成功")
        
        # 查询数据
        result = conn.execute("SELECT * FROM test").fetchall()
        print(f"✅ 查询成功，结果: {result}")
        
        # 测试聚合
        agg_result = conn.execute("SELECT COUNT(*), AVG(value) FROM test").fetchone()
        print(f"✅ 聚合查询成功: 记录数={agg_result[0]}, 平均值={agg_result[1]}")
        
        conn.close()
        print("✅ 连接关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ DuckDB功能测试失败: {e}")
        return False

def test_pandas_integration():
    """测试Pandas集成"""
    print("\n🧪 测试Pandas集成...")
    
    try:
        import duckdb
        import pandas as pd
        
        # 创建连接
        conn = duckdb.connect(':memory:')
        
        # 创建测试数据
        conn.execute("""
            CREATE TABLE test_pandas AS 
            SELECT 
                i as id,
                'item_' || i as name,
                random() * 100 as value
            FROM range(100) t(i)
        """)
        
        # 转换为DataFrame
        df = conn.execute("SELECT * FROM test_pandas").df()
        print(f"✅ DataFrame转换成功: {df.shape}")
        print(f"   前5行:\n{df.head()}")
        
        # 基本统计
        print(f"✅ 统计信息:\n{df['value'].describe()}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Pandas集成测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n🧪 测试文件操作...")
    
    try:
        import duckdb
        import tempfile
        import os
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.duckdb', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            # 连接到文件数据库
            conn = duckdb.connect(tmp_path)
            print(f"✅ 文件数据库连接成功: {tmp_path}")
            
            # 创建表并插入数据
            conn.execute("""
                CREATE TABLE file_test AS 
                SELECT 
                    i as id,
                    'data_' || i as name,
                    i * 1.5 as value
                FROM range(50) t(i)
            """)
            
            # 验证数据
            count = conn.execute("SELECT COUNT(*) FROM file_test").fetchone()[0]
            print(f"✅ 数据写入成功: {count} 条记录")
            
            conn.close()
            
            # 重新连接验证持久化
            conn2 = duckdb.connect(tmp_path)
            count2 = conn2.execute("SELECT COUNT(*) FROM file_test").fetchone()[0]
            print(f"✅ 数据持久化验证成功: {count2} 条记录")
            
            conn2.close()
            
        finally:
            # 清理临时文件
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
                print("✅ 临时文件清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🦆 DuckDB导入和功能测试")
    print("=" * 50)
    
    tests = [
        ("DuckDB导入", test_duckdb_import),
        ("基本功能", test_duckdb_basic_functionality),
        ("Pandas集成", test_pandas_integration),
        ("文件操作", test_file_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试总结:")
    print(f"   总测试数: {total}")
    print(f"   通过测试: {passed}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！DuckDB环境正常")
        print("\n💡 下一步:")
        print("   • 运行: python nclink_v2_duckdb.py")
        print("   • 体验DuckDB高性能数据分析")
    else:
        print(f"\n⚠️ {total-passed} 个测试失败")
        print("💡 请检查DuckDB安装: pip install duckdb")

if __name__ == "__main__":
    main()
