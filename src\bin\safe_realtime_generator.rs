/// 安全的实时数据写入器
/// 去除所有unsafe代码，确保稳定性

use std::fs::{File, create_dir_all};
use std::io::{Write, BufWriter};
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH, Instant};
use std::sync::{Arc, Mutex, mpsc};
use std::thread;
use byteorder::{LittleEndian, WriteBytesExt};
use serde_json::json;
use rand::Rng;

/// 安全的数据记录
#[derive(Debug, Clone)]
struct SafeRecord {
    id: u64,
    timestamp: u64,
    value: f64,
    quality: u32,
    point_id: String,
    device_id: String,
    tags: String,
}

/// 性能统计
#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lone)]
struct SafeStats {
    total_records: u64,
    min_latency_ns: u64,
    max_latency_ns: u64,
    avg_latency_ns: u64,
    p99_latency_ns: u64,
    throughput_per_sec: f64,
    total_time_seconds: f64,
}

/// 安全的实时生成器
struct SafeRealtimeGenerator {
    output_dir: String,
    use_compression: bool,
    stats: Arc<Mutex<SafeStats>>,
}

impl SafeRealtimeGenerator {
    fn new(output_dir: String, use_compression: bool) -> Self {
        Self {
            output_dir,
            use_compression,
            stats: Arc::new(Mutex::new(SafeStats::default())),
        }
    }
    
    /// 生成安全记录
    fn generate_safe_record(&self, index: usize) -> SafeRecord {
        let mut rng = rand::thread_rng();
        
        // 安全的时间戳生成
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos() as u64;
        
        // 简化的数据生成
        let plant_id = (index % 3) + 1;
        let unit_id = (index % 4) + 1;
        let point_type = match index % 4 {
            0 => "TI",
            1 => "PI",
            2 => "FI",
            _ => "LI",
        };
        
        let point_id = format!("{}_{}_{}_{:03}", plant_id, unit_id, point_type, index % 100);
        let device_id = format!("PLC_{}", plant_id);
        
        let base_value = match point_type {
            "TI" => 25.0,
            "PI" => 101.3,
            "FI" => 50.0,
            _ => 2.5,
        };
        
        let value = base_value + (rng.gen::<f64>() - 0.5) * 10.0;
        let quality = if rng.gen_bool(0.98) { 192 } else { 0 };
        
        let tags = json!({
            "type": point_type,
            "plant": plant_id,
            "unit": unit_id,
            "critical": index % 10 == 0
        }).to_string();
        
        SafeRecord {
            id: index as u64 + 1,
            timestamp,
            value,
            quality,
            point_id,
            device_id,
            tags,
        }
    }
    
    /// 序列化记录为二进制格式
    fn serialize_record(&self, record: &SafeRecord) -> Vec<u8> {
        let mut data = Vec::with_capacity(512);
        
        // 写入固定长度字段
        data.extend_from_slice(&record.id.to_le_bytes());
        data.extend_from_slice(&record.timestamp.to_le_bytes());
        data.extend_from_slice(&record.value.to_le_bytes());
        data.extend_from_slice(&record.quality.to_le_bytes());
        
        // 写入变长字符串字段
        let point_id_bytes = record.point_id.as_bytes();
        data.extend_from_slice(&(point_id_bytes.len() as u32).to_le_bytes());
        data.extend_from_slice(point_id_bytes);
        
        let device_id_bytes = record.device_id.as_bytes();
        data.extend_from_slice(&(device_id_bytes.len() as u32).to_le_bytes());
        data.extend_from_slice(device_id_bytes);
        
        let tags_bytes = record.tags.as_bytes();
        data.extend_from_slice(&(tags_bytes.len() as u32).to_le_bytes());
        data.extend_from_slice(tags_bytes);
        
        data
    }
    
    /// 无压缩直写策略
    fn strategy_no_compression(&self, record_count: usize) -> Result<(), String> {
        println!("🚀 使用安全无压缩直写策略");
        
        let db_path = Path::new(&self.output_dir).join("safe_realtime.duckdb");
        let wal_path = Path::new(&self.output_dir).join("safe_realtime.duckdb.wal");
        
        // 创建文件写入器
        let mut db_writer = BufWriter::with_capacity(
            1024 * 1024, // 1MB缓冲区
            File::create(&db_path).map_err(|e| format!("创建数据库文件失败: {}", e))?
        );
        
        let mut wal_writer = BufWriter::with_capacity(
            64 * 1024, // 64KB缓冲区
            File::create(&wal_path).map_err(|e| format!("创建WAL文件失败: {}", e))?
        );
        
        let start_time = Instant::now();
        
        // 对于大数据量，采样延迟数据以节省内存
        let sample_rate = if record_count > 1_000_000 { 1000 } else { 1 };
        let mut latencies = Vec::with_capacity(record_count / sample_rate);
        
        println!("⚡ 开始安全实时写入测试...");
        println!("   记录数量: {}", format_number(record_count));
        println!("   采样率: 1/{}", sample_rate);
        
        for i in 0..record_count {
            let record_start = Instant::now();
            
            // 生成记录
            let record = self.generate_safe_record(i);
            
            // 序列化
            let data = self.serialize_record(&record);
            
            // 写入数据文件
            db_writer.write_all(&data)
                .map_err(|e| format!("写入数据失败: {}", e))?;
            
            // 写入WAL
            wal_writer.write_u64::<LittleEndian>(record.timestamp)
                .map_err(|e| format!("写入WAL时间戳失败: {}", e))?;
            wal_writer.write_u64::<LittleEndian>(record.id)
                .map_err(|e| format!("写入WAL ID失败: {}", e))?;
            
            let latency = record_start.elapsed().as_nanos() as u64;
            
            // 采样延迟数据
            if i % sample_rate == 0 {
                latencies.push(latency);
            }
            
            // 定期刷新
            if i % 10000 == 0 {
                db_writer.flush().map_err(|e| format!("刷新数据文件失败: {}", e))?;
                wal_writer.flush().map_err(|e| format!("刷新WAL文件失败: {}", e))?;
            }
            
            // 进度报告
            let report_interval = if record_count > 1_000_000 { 100_000 } else { 10_000 };
            if i % report_interval == 0 && i > 0 {
                let elapsed = start_time.elapsed().as_secs_f64();
                let current_rate = i as f64 / elapsed;
                let eta_seconds = (record_count - i) as f64 / current_rate;
                
                let recent_samples = std::cmp::min(latencies.len(), 100);
                let recent_start = latencies.len().saturating_sub(recent_samples);
                let avg_latency = if recent_samples > 0 {
                    latencies[recent_start..].iter().sum::<u64>() / recent_samples as u64
                } else {
                    0
                };
                
                println!("📊 进度: {} 条 ({:.1}%) | 延迟: {:.2} μs | 速度: {:.0} 条/秒 | ETA: {:.1}分钟", 
                         format_number(i + 1),
                         (i + 1) as f64 / record_count as f64 * 100.0,
                         avg_latency as f64 / 1000.0,
                         current_rate,
                         eta_seconds / 60.0);
            }
            
            // 模拟1毫秒间隔（仅在小数据量测试时）
            if record_count <= 100_000 && i < record_count - 1 {
                std::thread::sleep(std::time::Duration::from_micros(1000));
            }
        }
        
        // 最终刷新
        db_writer.flush().map_err(|e| format!("最终刷新数据文件失败: {}", e))?;
        wal_writer.flush().map_err(|e| format!("最终刷新WAL文件失败: {}", e))?;
        
        let total_time = start_time.elapsed();
        
        // 计算统计信息
        if !latencies.is_empty() {
            latencies.sort_unstable();
            let min_latency = latencies[0];
            let max_latency = latencies[latencies.len() - 1];
            let avg_latency = latencies.iter().sum::<u64>() / latencies.len() as u64;
            let p99_index = ((latencies.len() as f64) * 0.99) as usize;
            let p99_latency = latencies[std::cmp::min(p99_index, latencies.len() - 1)];
            let throughput = record_count as f64 / total_time.as_secs_f64();
            
            // 更新统计
            if let Ok(mut stats) = self.stats.lock() {
                stats.total_records = record_count as u64;
                stats.min_latency_ns = min_latency;
                stats.max_latency_ns = max_latency;
                stats.avg_latency_ns = avg_latency;
                stats.p99_latency_ns = p99_latency;
                stats.throughput_per_sec = throughput;
                stats.total_time_seconds = total_time.as_secs_f64();
            }
            
            println!("\n✅ 安全实时写入完成!");
            self.print_safe_stats();
        }
        
        Ok(())
    }
    
    /// LZ4异步压缩策略
    fn strategy_lz4_async(&self, record_count: usize) -> Result<(), String> {
        println!("🔄 使用安全LZ4异步压缩策略");
        
        let db_path = Path::new(&self.output_dir).join("safe_lz4.duckdb");
        let wal_path = Path::new(&self.output_dir).join("safe_lz4.duckdb.wal");
        
        // 创建通道
        let (tx, rx) = mpsc::channel::<Vec<u8>>();
        let db_path_clone = db_path.clone();
        
        // 启动压缩线程
        let compression_handle = thread::spawn(move || {
            let mut writer = BufWriter::with_capacity(
                2 * 1024 * 1024,
                File::create(&db_path_clone).expect("创建压缩文件失败")
            );
            
            let mut total_compressed = 0;
            while let Ok(data) = rx.recv() {
                let compressed = lz4_flex::compress_prepend_size(&data);
                writer.write_all(&compressed).expect("写入压缩数据失败");
                total_compressed += 1;
                
                if total_compressed % 1000 == 0 {
                    writer.flush().expect("刷新压缩文件失败");
                }
            }
            
            writer.flush().expect("最终刷新失败");
            println!("🗜️ 异步压缩完成，处理 {} 批数据", total_compressed);
        });
        
        // 主线程写入
        let mut wal_writer = BufWriter::new(
            File::create(&wal_path).map_err(|e| format!("创建WAL文件失败: {}", e))?
        );
        
        let start_time = Instant::now();
        let buffer_size = 1000; // 1000条记录一批
        let mut buffer = Vec::with_capacity(buffer_size * 512);
        
        println!("⚡ 开始安全异步压缩测试...");
        
        for i in 0..record_count {
            let record = self.generate_safe_record(i);
            let data = self.serialize_record(&record);
            
            buffer.extend_from_slice(&data);
            
            // 写入WAL
            wal_writer.write_u64::<LittleEndian>(record.timestamp)
                .map_err(|e| format!("写入WAL失败: {}", e))?;
            
            // 批量发送压缩
            if buffer.len() >= buffer_size * 512 || i == record_count - 1 {
                tx.send(buffer.clone()).map_err(|e| format!("发送数据失败: {}", e))?;
                buffer.clear();
            }
            
            // 进度报告
            if i % 100_000 == 0 && i > 0 {
                let elapsed = start_time.elapsed().as_secs_f64();
                let rate = i as f64 / elapsed;
                println!("📊 已处理: {} 条 | 速度: {:.0} 条/秒", format_number(i + 1), rate);
            }
        }
        
        // 关闭发送端，等待压缩完成
        drop(tx);
        compression_handle.join().expect("压缩线程结束失败");
        wal_writer.flush().map_err(|e| format!("刷新WAL失败: {}", e))?;
        
        let total_time = start_time.elapsed();
        let throughput = record_count as f64 / total_time.as_secs_f64();
        
        println!("\n✅ 安全LZ4异步压缩完成!");
        println!("📈 性能统计:");
        println!("   总记录数: {}", format_number(record_count));
        println!("   总耗时: {:.2} 秒", total_time.as_secs_f64());
        println!("   吞吐量: {:.0} 条/秒", throughput);
        
        Ok(())
    }
    
    /// 打印安全统计
    fn print_safe_stats(&self) {
        if let Ok(stats) = self.stats.lock() {
            println!("📈 安全性能统计:");
            println!("   总记录数: {}", format_number(stats.total_records as usize));
            println!("   总耗时: {:.2} 秒", stats.total_time_seconds);
            println!("   平均延迟: {:.2} μs", stats.avg_latency_ns as f64 / 1000.0);
            println!("   P99延迟: {:.2} μs", stats.p99_latency_ns as f64 / 1000.0);
            println!("   最小延迟: {:.2} μs", stats.min_latency_ns as f64 / 1000.0);
            println!("   最大延迟: {:.2} μs", stats.max_latency_ns as f64 / 1000.0);
            println!("   吞吐量: {:.0} 条/秒", stats.throughput_per_sec);
        }
    }
    
    /// 显示文件信息
    fn show_file_info(&self) -> Result<(), String> {
        println!("\n💾 生成的文件:");
        
        let files = if self.use_compression {
            vec!["safe_lz4.duckdb", "safe_lz4.duckdb.wal"]
        } else {
            vec!["safe_realtime.duckdb", "safe_realtime.duckdb.wal"]
        };
        
        for file_name in files {
            let file_path = Path::new(&self.output_dir).join(file_name);
            if file_path.exists() {
                let size = file_path.metadata()
                    .map_err(|e| format!("获取文件大小失败: {}", e))?
                    .len() as f64 / (1024.0 * 1024.0);
                println!("   📄 {}: {:.2} MB", file_name, size);
            }
        }
        
        Ok(())
    }
    
    /// 运行安全测试
    pub fn run_safe_test(&self, record_count: usize) -> Result<(), String> {
        println!("🛡️ 安全实时数据写入测试");
        println!("{}", "=".repeat(60));
        
        create_dir_all(&self.output_dir)
            .map_err(|e| format!("创建目录失败: {}", e))?;
        
        println!("📊 测试配置:");
        println!("   输出目录: {}", self.output_dir);
        println!("   记录数量: {}", format_number(record_count));
        println!("   压缩模式: {}", if self.use_compression { "LZ4异步" } else { "无压缩" });
        println!("   预计数据量: {:.2} GB", (record_count * 512) as f64 / (1024.0 * 1024.0 * 1024.0));
        
        if self.use_compression {
            self.strategy_lz4_async(record_count)
        } else {
            self.strategy_no_compression(record_count)
        }
    }
}

/// 格式化数字
fn format_number(n: usize) -> String {
    let s = n.to_string();
    let mut result = String::new();
    for (i, c) in s.chars().rev().enumerate() {
        if i > 0 && i % 3 == 0 {
            result.push(',');
        }
        result.push(c);
    }
    result.chars().rev().collect()
}

fn main() {
    println!("🛡️ 安全实时数据写入测试");
    println!("{}", "=".repeat(70));
    
    let output_dir = r"D:\database".to_string();
    let record_count = 10_000_000; // 1000万条数据
    
    println!("🧪 测试安全策略...\n");
    
    // 测试1: 安全无压缩
    println!("测试1: 安全无压缩直写");
    let generator1 = SafeRealtimeGenerator::new(output_dir.clone(), false);
    
    match generator1.run_safe_test(record_count) {
        Ok(()) => {
            generator1.show_file_info().unwrap_or_default();
        }
        Err(e) => eprintln!("❌ 测试1失败: {}", e),
    }
    
    println!("\n{}", "-".repeat(50));
    
    // 测试2: 安全LZ4压缩
    println!("测试2: 安全LZ4异步压缩");
    let generator2 = SafeRealtimeGenerator::new(output_dir.clone(), true);
    
    match generator2.run_safe_test(record_count) {
        Ok(()) => {
            generator2.show_file_info().unwrap_or_default();
        }
        Err(e) => eprintln!("❌ 测试2失败: {}", e),
    }
    
    println!("\n🎉 安全测试完成!");
    println!("\n💡 安全优化特点:");
    println!("   • 去除所有unsafe代码");
    println!("   • 内存安全保证");
    println!("   • 错误处理完善");
    println!("   • 适合生产环境");
}
