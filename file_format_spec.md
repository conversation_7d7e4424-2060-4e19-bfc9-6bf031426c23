# nclink_v2.duckdb 文件格式规范

## 文件结构

### 1. 主数据库文件 (nclink_v2.duckdb)

```
文件头 (64字节)
├── 魔数: "DUCKDB\x00\x01" (8字节)
├── 版本: uint32 (4字节)
├── 页面大小: uint32 (4字节) 
├── 压缩类型: uint32 (4字节)
├── 创建时间: uint64 (8字节)
├── 修改时间: uint64 (8字节)
├── 记录数: uint64 (8字节)
├── 压缩比: double (8字节)
└── 校验和: uint32 (4字节)

数据页面
├── 页面1: 压缩数据块
├── 页面2: 压缩数据块
└── ...
```

### 2. WAL文件 (nclink_v2.duckdb.wal)

```
WAL记录1
├── 记录类型: uint32 (4字节)
├── 事务ID: uint64 (8字节)
├── 时间戳: uint64 (8字节)
└── 数据大小: uint64 (8字节)

WAL记录2
└── ...
```

## 数据类型定义

### 压缩类型
- 0: None
- 1: LZ4
- 2: Gzip
- 3: Snappy
- 4: ZSTD

### WAL记录类型
- 1: Insert
- 2: Update
- 3: Delete
- 4: Commit
- 5: Rollback

### 工程数据记录
```sql
CREATE TABLE engineering_data (
    id INTEGER PRIMARY KEY,
    point_id TEXT NOT NULL,      -- 测点ID
    timestamp INTEGER NOT NULL,  -- Unix时间戳
    value REAL NOT NULL,         -- 数值
    quality INTEGER NOT NULL,    -- 质量码
    device_id TEXT NOT NULL,     -- 设备ID
    tags TEXT NOT NULL           -- JSON标签
);
```

## 跨平台使用方法

### Python
```python
import sqlite3
conn = sqlite3.connect('nclink_v2.duckdb')
cursor = conn.cursor()
cursor.execute('SELECT * FROM engineering_data LIMIT 10')
data = cursor.fetchall()
```

### Node.js
```javascript
const sqlite3 = require('sqlite3');
const db = new sqlite3.Database('nclink_v2.duckdb');
db.all('SELECT * FROM engineering_data LIMIT 10', (err, rows) => {
    console.log(rows);
});
```

### C#
```csharp
using System.Data.SQLite;
var conn = new SQLiteConnection("Data Source=nclink_v2.duckdb");
conn.Open();
var cmd = new SQLiteCommand("SELECT * FROM engineering_data LIMIT 10", conn);
var reader = cmd.ExecuteReader();
```

### Java
```java
import java.sql.*;
Connection conn = DriverManager.getConnection("****************************");
Statement stmt = conn.createStatement();
ResultSet rs = stmt.executeQuery("SELECT * FROM engineering_data LIMIT 10");
```

### Go
```go
import "database/sql"
import _ "github.com/mattn/go-sqlite3"

db, _ := sql.Open("sqlite3", "nclink_v2.duckdb")
rows, _ := db.Query("SELECT * FROM engineering_data LIMIT 10")
```

### Rust
```rust
use rusqlite::Connection;
let conn = Connection::open("nclink_v2.duckdb")?;
let mut stmt = conn.prepare("SELECT * FROM engineering_data LIMIT 10")?;
let rows = stmt.query_map([], |row| { ... })?;
```

## 文件传输

### 1. 完整传输
```bash
# 复制两个文件
cp nclink_v2.duckdb /target/path/
cp nclink_v2.duckdb.wal /target/path/
```

### 2. 网络传输
```bash
# SCP传输
scp nclink_v2.duckdb user@server:/path/
scp nclink_v2.duckdb.wal user@server:/path/

# HTTP传输
curl -X POST -F "file=@nclink_v2.duckdb" http://server/upload
curl -X POST -F "file=@nclink_v2.duckdb.wal" http://server/upload
```

### 3. 压缩传输
```bash
# 打包压缩
tar -czf nclink_v2.tar.gz nclink_v2.duckdb nclink_v2.duckdb.wal

# 解压
tar -xzf nclink_v2.tar.gz
```

## 兼容性说明

### 支持的平台
- ✅ Windows (x86_64, ARM64)
- ✅ Linux (x86_64, ARM64, RISC-V)
- ✅ macOS (Intel, Apple Silicon)
- ✅ FreeBSD, OpenBSD, NetBSD
- ✅ Android, iOS

### 字节序
- 使用小端序 (Little Endian)
- 所有平台自动处理字节序转换

### 文件系统
- 支持所有主流文件系统
- 无特殊权限要求
- 支持网络文件系统 (NFS, SMB等)

## 性能优化

### 读取优化
```sql
-- 创建索引
CREATE INDEX idx_timestamp ON engineering_data(timestamp);
CREATE INDEX idx_point_id ON engineering_data(point_id);
CREATE INDEX idx_device_id ON engineering_data(device_id);

-- 优化查询
SELECT * FROM engineering_data 
WHERE timestamp BETWEEN ? AND ? 
ORDER BY timestamp 
LIMIT 1000;
```

### 内存优化
```python
# 分批读取大量数据
def read_in_batches(conn, batch_size=10000):
    offset = 0
    while True:
        cursor = conn.execute(
            f"SELECT * FROM engineering_data LIMIT {batch_size} OFFSET {offset}"
        )
        batch = cursor.fetchall()
        if not batch:
            break
        yield batch
        offset += batch_size
```

## 故障排除

### 常见问题
1. **文件损坏**: 检查文件大小和魔数
2. **权限问题**: 确保读取权限
3. **版本不兼容**: 检查文件头版本号
4. **字符编码**: 使用UTF-8编码

### 验证文件完整性
```python
def verify_file(db_path):
    with open(db_path, 'rb') as f:
        magic = f.read(8)
        if magic != b"DUCKDB\x00\x01":
            return False, "无效的魔数"
        
        version = struct.unpack('<I', f.read(4))[0]
        if version > 1:
            return False, f"不支持的版本: {version}"
        
        return True, "文件有效"
```
