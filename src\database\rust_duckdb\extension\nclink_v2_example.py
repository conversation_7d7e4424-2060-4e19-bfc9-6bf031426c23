#!/usr/bin/env python3
"""
nclink_v2数据库使用示例
简化的CRUD操作示例，适合快速上手
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from nclink_v2_crud import NclinkV2Database
import pandas as pd
from datetime import datetime

def basic_crud_example():
    """基础CRUD操作示例"""
    print("🚀 nclink_v2数据库基础操作示例")
    print("=" * 50)
    
    # 连接数据库
    db_path = r"D:\database\nclink_v2.duckdb"
    db = NclinkV2Database(db_path)
    
    try:
        # 1. 查看数据库概况
        print("\n📊 数据库概况:")
        stats = db.get_statistics()
        print(f"   总记录数: {stats['total_records']:,}")
        print(f"   数据类型: {list(stats['data_type_distribution'].keys())}")
        
        # 2. 读取数据示例
        print("\n📖 读取数据示例:")
        
        # 读取最新10条记录
        latest_records = db.read_records(limit=10)
        print(f"   最新10条记录:")
        for i, record in enumerate(latest_records[:3], 1):
            print(f"   {i}. ID={record['id']}, Point={record['point_id']}, "
                  f"Value={record['value']:.2f}, Type={record['data_type']}")
        
        # 按条件查询
        temp_records = db.read_records(
            limit=5, 
            filters={'data_type': 'TI'}  # 查询温度数据
        )
        print(f"\n   温度数据 (TI类型):")
        for record in temp_records[:3]:
            print(f"   Point={record['point_id']}, Temperature={record['value']:.1f}°C")
        
        # 3. 创建数据示例
        print("\n➕ 创建数据示例:")
        new_id = db.create_record(
            point_id="DEMO_TI_001",
            value=25.6,
            quality=192,
            device_id="PLC_DEMO",
            data_type="TI",
            unit="°C",
            plant_id=1,
            tags={"demo": True, "location": "测试区域"}
        )
        print(f"   创建成功，新记录ID: {new_id}")
        
        # 4. 更新数据示例
        print("\n✏️ 更新数据示例:")
        if new_id:
            success = db.update_record(new_id, {
                'value': 26.2,
                'tags': {"demo": True, "location": "测试区域", "updated": True}
            })
            if success:
                print(f"   记录 {new_id} 更新成功")
        
        # 5. 数据分析示例
        print("\n📊 数据分析示例:")
        df = db.get_dataframe(limit=1000)
        
        print(f"   数据集形状: {df.shape}")
        print(f"   数值范围: {df['value'].min():.2f} ~ {df['value'].max():.2f}")
        print(f"   平均值: {df['value'].mean():.2f}")
        
        # 按数据类型分组统计
        type_summary = df.groupby('data_type')['value'].agg(['count', 'mean']).round(2)
        print(f"\n   按类型统计:")
        for data_type, stats in type_summary.iterrows():
            print(f"   {data_type}: {stats['count']} 条, 均值 {stats['mean']}")
        
        # 6. 删除数据示例
        print("\n🗑️ 删除数据示例:")
        if new_id:
            success = db.delete_record(new_id)
            if success:
                print(f"   演示记录 {new_id} 删除成功")
        
        print("\n✅ 基础操作示例完成!")
        
    finally:
        db.close()

def advanced_query_example():
    """高级查询示例"""
    print("\n🔍 高级查询示例")
    print("-" * 30)
    
    db_path = r"D:\database\nclink_v2.duckdb"
    db = NclinkV2Database(db_path)
    
    try:
        # 1. 数值范围查询
        print("\n1. 数值范围查询 (温度 20-30°C):")
        temp_range = db.read_records(
            limit=5,
            filters={
                'data_type': 'TI',
                'value_range': [20.0, 30.0]
            }
        )
        for record in temp_range:
            print(f"   {record['point_id']}: {record['value']:.1f}°C")
        
        # 2. 设备查询
        print("\n2. 特定设备查询 (PLC_01):")
        device_data = db.read_records(
            limit=5,
            filters={'device_id': 'PLC_01'}
        )
        for record in device_data:
            print(f"   {record['point_id']}: {record['value']:.2f} {record['unit']}")
        
        # 3. 质量过滤
        print("\n3. 数据质量过滤 (质量=192):")
        good_quality = db.read_records(
            limit=5,
            filters={'quality': 192}
        )
        print(f"   找到 {len(good_quality)} 条高质量数据")
        
        # 4. 使用Pandas进行复杂分析
        print("\n4. Pandas复杂分析:")
        df = db.get_dataframe(limit=5000)
        
        # 按工厂和数据类型分组
        plant_type_stats = df.groupby(['plant_id', 'data_type'])['value'].agg([
            'count', 'mean', 'std'
        ]).round(2)
        
        print("   按工厂和类型分组统计 (前5行):")
        print(plant_type_stats.head())
        
        # 数据质量分析
        quality_rate = (df['quality'] == 192).mean() * 100
        print(f"\n   整体数据质量率: {quality_rate:.1f}%")
        
    finally:
        db.close()

def data_export_example():
    """数据导出示例"""
    print("\n💾 数据导出示例")
    print("-" * 30)
    
    db_path = r"D:\database\nclink_v2.duckdb"
    db = NclinkV2Database(db_path)
    
    try:
        # 获取数据
        df = db.get_dataframe(limit=1000)
        
        # 导出为CSV
        csv_file = "nclink_v2_sample.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        print(f"✅ 已导出 {len(df)} 条记录到: {csv_file}")
        
        # 导出温度数据
        temp_df = df[df['data_type'] == 'TI'].copy()
        temp_csv = "nclink_v2_temperature.csv"
        temp_df.to_csv(temp_csv, index=False, encoding='utf-8-sig')
        print(f"✅ 已导出 {len(temp_df)} 条温度记录到: {temp_csv}")
        
        # 统计报告
        print(f"\n📊 导出统计:")
        print(f"   总记录数: {len(df)}")
        print(f"   温度记录: {len(temp_df)}")
        print(f"   数据类型: {df['data_type'].nunique()} 种")
        print(f"   设备数量: {df['device_id'].nunique()} 个")
        
    finally:
        db.close()

def batch_operations_example():
    """批量操作示例"""
    print("\n⚡ 批量操作示例")
    print("-" * 30)
    
    db_path = r"D:\database\nclink_v2.duckdb"
    db = NclinkV2Database(db_path)
    
    try:
        # 批量创建测试数据
        print("\n1. 批量创建测试数据:")
        test_ids = []
        
        for i in range(5):
            new_id = db.create_record(
                point_id=f"BATCH_TEST_{i:03d}",
                value=20.0 + i * 2.5,
                quality=192,
                device_id="PLC_BATCH",
                data_type="TI",
                unit="°C",
                plant_id=1,
                tags={"batch_test": True, "sequence": i}
            )
            test_ids.append(new_id)
        
        print(f"   创建了 {len(test_ids)} 条测试记录")
        
        # 批量查询
        print("\n2. 批量查询测试数据:")
        test_records = db.read_records(
            limit=10,
            filters={'device_id': 'PLC_BATCH'}
        )
        
        for record in test_records:
            print(f"   {record['point_id']}: {record['value']:.1f}°C")
        
        # 批量删除
        print("\n3. 批量删除测试数据:")
        deleted_count = db.delete_records({'device_id': 'PLC_BATCH'})
        print(f"   删除了 {deleted_count} 条测试记录")
        
    finally:
        db.close()

def main():
    """主函数"""
    print("🎯 nclink_v2数据库完整使用示例")
    print("=" * 60)
    
    try:
        # 基础CRUD操作
        basic_crud_example()
        
        # 高级查询
        advanced_query_example()
        
        # 数据导出
        data_export_example()
        
        # 批量操作
        batch_operations_example()
        
        print("\n🎉 所有示例执行完成!")
        print("\n💡 使用提示:")
        print("   • 运行 python nclink_v2_crud.py --interactive 进入交互模式")
        print("   • 查看 nclink_v2_crud.py 了解完整API")
        print("   • 使用 Pandas 进行复杂数据分析")
        print("   • 支持 CSV/JSON/Excel 多种导出格式")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
