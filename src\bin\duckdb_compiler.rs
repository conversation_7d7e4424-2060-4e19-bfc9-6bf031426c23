/// DuckDB数据库编译器
/// 
/// 将工程数据编译成标准的nclink_v2.duckdb和nclink_v2.duckdb.wal文件
/// 自动选择最优压缩算法，确保跨平台兼容性

use nclink_v2_rust::database::rust_duckdb::storage::duckdb_file_manager::{DuckDBFileManager, EngineeringDataPoint};
use std::time::{SystemTime, UNIX_EPOCH, Instant};
use std::path::Path;
use rand::Rng;
use serde_json::json;

/// 格式化数字，添加千位分隔符
fn format_number(n: usize) -> String {
    let s = n.to_string();
    let mut result = String::new();
    for (i, c) in s.chars().rev().enumerate() {
        if i > 0 && i % 3 == 0 {
            result.push(',');
        }
        result.push(c);
    }
    result.chars().rev().collect()
}

/// 工程数据生成器
struct EngineeringDataCompiler {
    /// 输出文件路径
    output_path: String,
    /// 记录数量
    record_count: usize,
    /// 批次大小
    batch_size: usize,
}

impl EngineeringDataCompiler {
    /// 创建新的编译器
    pub fn new(output_path: String, record_count: usize, batch_size: usize) -> Self {
        Self {
            output_path,
            record_count,
            batch_size,
        }
    }
    
    /// 生成工程数据点
    fn generate_engineering_data_point(&self, index: usize) -> EngineeringDataPoint {
        let mut rng = rand::thread_rng();
        
        // 工程设备配置
        let plants = ["Plant_A", "Plant_B", "Plant_C"];
        let units = ["Unit_01", "Unit_02", "Unit_03", "Unit_04"];
        let systems = ["Reactor", "Distillation", "Compressor", "Heat_Exchanger", "Pump"];
        let point_types = ["TI", "PI", "FI", "LI", "AI", "VI", "SI"];
        
        // 选择设备配置
        let plant = plants[index % plants.len()];
        let unit = units[(index / plants.len()) % units.len()];
        let system = systems[(index / (plants.len() * units.len())) % systems.len()];
        let point_type = point_types[index % point_types.len()];
        
        // 回路编号
        let loop_num = (index / point_types.len()) % 100 + 1;
        
        // 生成测点ID
        let point_id = format!("{}_{}_{}_{:03}", plant, unit, system, loop_num);
        
        // 生成时间戳
        let base_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() - 86400; // 从24小时前开始
        let timestamp = base_time + (index as u64);
        
        // 生成真实的工程数值
        let value = self.generate_realistic_value(point_type, index);
        
        // 生成质量码
        let quality = if rng.gen_bool(0.95) { 192 } else { 0 };
        
        // 生成设备ID
        let device_id = format!("{}_{}_{}_PLC", plant, unit, system);
        
        // 生成结构化标签
        let tags = self.generate_engineering_tags(plant, unit, system, point_type, loop_num);
        
        EngineeringDataPoint {
            point_id,
            timestamp,
            value,
            quality,
            device_id,
            tags,
        }
    }
    
    /// 生成真实的工程数值
    fn generate_realistic_value(&self, point_type: &str, index: usize) -> f64 {
        let base_values = match point_type {
            "TI" => 25.0,   // 温度
            "PI" => 101.3,  // 压力
            "FI" => 50.0,   // 流量
            "LI" => 2.5,    // 液位
            "AI" => 12.0,   // 模拟量
            "VI" => 5.0,    // 振动
            "SI" => 1500.0, // 转速
            _ => 50.0,
        };
        
        // 添加周期性变化和噪声
        let cycle = (index as f64 * 0.01).sin() * 0.1;
        let noise = ((index * 17) % 100) as f64 / 1000.0 - 0.05;
        
        let value = base_values * (1.0 + cycle + noise);
        
        // 量化到合理精度
        (value * 100.0).round() / 100.0
    }
    
    /// 生成工程标签
    fn generate_engineering_tags(&self, plant: &str, unit: &str, system: &str, point_type: &str, loop_num: usize) -> String {
        let unit_map = match point_type {
            "TI" => "°C",
            "PI" => "kPa",
            "FI" => "m³/h",
            "LI" => "m",
            "AI" => "mA",
            "VI" => "mm/s",
            "SI" => "rpm",
            _ => "",
        };
        
        json!({
            "plant": plant,
            "unit": unit,
            "system": system,
            "type": point_type,
            "unit": unit_map,
            "loop": format!("L{:03}", loop_num),
            "critical": loop_num % 10 == 0,
            "description": format!("{} {} in {} {}", point_type, system, plant, unit)
        }).to_string()
    }
    
    /// 编译数据库
    pub fn compile(&self) -> Result<(), String> {
        println!("🔨 DuckDB数据库编译器");
        println!("{}", "=".repeat(50));
        
        println!("📊 编译配置:");
        println!("   输出文件: {}", self.output_path);
        println!("   记录数量: {}", format_number(self.record_count));
        println!("   批次大小: {}", format_number(self.batch_size));
        
        let start_time = Instant::now();
        
        // 创建DuckDB文件管理器
        let mut db_manager = DuckDBFileManager::new(&self.output_path)?;
        db_manager.initialize()?;
        
        println!("\n🔄 开始编译数据...");
        
        let mut total_inserted = 0;
        let progress_interval = self.record_count / 20; // 5%进度间隔
        
        // 分批生成和写入数据
        for start_idx in (0..self.record_count).step_by(self.batch_size) {
            let end_idx = (start_idx + self.batch_size).min(self.record_count);
            let batch_size_actual = end_idx - start_idx;
            
            // 生成批次数据
            let mut batch_data = Vec::with_capacity(batch_size_actual);
            for i in start_idx..end_idx {
                let data_point = self.generate_engineering_data_point(i);
                batch_data.push(data_point);
            }
            
            // 写入工程数据
            db_manager.write_engineering_data(&batch_data)?;
            total_inserted += batch_size_actual;
            
            // 显示进度
            if total_inserted % progress_interval == 0 || total_inserted == self.record_count {
                let elapsed = start_time.elapsed();
                let rate = total_inserted as f64 / elapsed.as_secs_f64();
                let progress = (total_inserted as f64 / self.record_count as f64) * 100.0;
                
                println!("📈 进度: {:.1}% | 已编译: {} 条 | 速度: {:.0} 条/秒",
                         progress, format_number(total_inserted), rate);
            }
        }
        
        // 提交事务
        db_manager.commit()?;
        
        let total_time = start_time.elapsed();
        let avg_rate = total_inserted as f64 / total_time.as_secs_f64();
        
        println!("\n✅ 编译完成!");
        println!("   总记录数: {}", format_number(total_inserted));
        println!("   总耗时: {:.2} 秒", total_time.as_secs_f64());
        println!("   平均速度: {:.0} 条/秒", avg_rate);
        
        // 显示数据库信息
        let info = db_manager.get_info();
        println!("\n🗄️ 数据库信息:");
        for (key, value) in &info {
            match key.as_str() {
                "database_path" => println!("   📄 数据库文件: {}", value),
                "wal_path" => println!("   📝 WAL文件: {}", value),
                "compression_type" => println!("   🗜️ 压缩算法: {}", value),
                "compression_ratio" => println!("   📊 压缩比: {}:1", value),
                "record_count" => println!("   📈 记录数: {}", value),
                "cross_platform_compatible" => {
                    if value == "true" {
                        println!("   ✅ 跨平台兼容: 是");
                    }
                }
                _ => {}
            }
        }
        
        // 显示文件大小
        self.show_file_sizes()?;
        
        // 关闭数据库
        db_manager.close()?;
        
        println!("\n🎉 DuckDB数据库编译成功!");
        println!("💡 提示:");
        println!("   - 生成的文件完全跨平台兼容");
        println!("   - 自动选择了最优压缩算法");
        println!("   - 可以在任何支持的平台上使用");
        
        Ok(())
    }
    
    /// 显示文件大小
    fn show_file_sizes(&self) -> Result<(), String> {
        let db_path = Path::new(&self.output_path);
        let wal_path = db_path.with_extension("duckdb.wal");
        
        println!("\n💾 文件大小:");
        
        if db_path.exists() {
            let db_size = db_path.metadata()
                .map_err(|e| format!("获取数据库文件大小失败: {}", e))?
                .len() as f64 / (1024.0 * 1024.0);
            println!("   📄 {}: {:.2} MB", db_path.file_name().unwrap().to_string_lossy(), db_size);
        }
        
        if wal_path.exists() {
            let wal_size = wal_path.metadata()
                .map_err(|e| format!("获取WAL文件大小失败: {}", e))?
                .len() as f64 / (1024.0 * 1024.0);
            println!("   📝 {}: {:.2} MB", wal_path.file_name().unwrap().to_string_lossy(), wal_size);
        }
        
        Ok(())
    }
}

fn main() {
    println!("🚀 DuckDB跨平台数据库编译器");
    println!("{}", "=".repeat(60));
    
    // 编译配置
    let output_path = "nclink_v2.duckdb".to_string();
    let record_count = 1_000_000; // 100万条记录
    let batch_size = 10_000;      // 1万条/批
    
    // 创建编译器
    let compiler = EngineeringDataCompiler::new(output_path, record_count, batch_size);
    
    // 执行编译
    match compiler.compile() {
        Ok(()) => {
            println!("\n🎊 编译成功完成!");
            
            // 显示使用说明
            println!("\n📖 使用说明:");
            println!("   1. 生成的文件可以在以下平台使用:");
            println!("      • Windows (x86_64, ARM64)");
            println!("      • Linux (x86_64, ARM64, RISC-V)");
            println!("      • macOS (x86_64, Apple Silicon)");
            println!("      • FreeBSD, OpenBSD, NetBSD");
            println!("   2. 文件传输:");
            println!("      • 直接复制 nclink_v2.duckdb 和 nclink_v2.duckdb.wal");
            println!("      • 无需任何转换或特殊处理");
            println!("   3. 压缩优化:");
            println!("      • 自动选择最优压缩算法");
            println!("      • 最大化存储效率");
            println!("      • 保持高性能访问");
        }
        Err(e) => {
            eprintln!("❌ 编译失败: {}", e);
            std::process::exit(1);
        }
    }
    
    // 等待用户输入
    println!("\n按 Enter 键退出...");
    let mut input = String::new();
    std::io::stdin().read_line(&mut input).expect("读取输入失败");
}
