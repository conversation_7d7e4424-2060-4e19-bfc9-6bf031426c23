#!/usr/bin/env python3
"""
NC-Link V2 DuckDB连接程序
连接并操作内存数据库和文件数据库
"""

import duckdb
import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

class NcLinkDBConnector:
    """NC-Link V2 DuckDB连接器"""
    
    def __init__(self, 
                 file_db_path: str = "./nclink_v2.duckdb", 
                 memory_db_name: str = ":memory:nc-link-v2"):
        """初始化连接器
        
        Args:
            file_db_path: 文件数据库路径
            memory_db_name: 内存数据库名称
        """
        self.file_db_path = Path(file_db_path)
        self.memory_db_name = memory_db_name
        self.file_conn = None
        self.memory_conn = None
        
    def connect(self) -> Tuple[bool, bool]:
        """连接到内存数据库和文件数据库
        
        Returns:
            Tuple[bool, bool]: (内存数据库连接状态, 文件数据库连接状态)
        """
        memory_success = False
        file_success = False
        
        # 连接内存数据库
        try:
            self.memory_conn = duckdb.connect(self.memory_db_name)
            print(f"✅ 成功连接到内存数据库: {self.memory_db_name}")
            memory_success = True
        except Exception as e:
            print(f"❌ 连接内存数据库失败: {e}")
        
        # 连接文件数据库
        if self.file_db_path.exists():
            try:
                self.file_conn = duckdb.connect(str(self.file_db_path))
                print(f"✅ 成功连接到文件数据库: {self.file_db_path}")
                file_success = True
            except Exception as e:
                print(f"❌ 连接文件数据库失败: {e}")
        else:
            print(f"⚠️ 警告: 文件数据库不存在: {self.file_db_path}")
        
        return memory_success, file_success
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self.memory_conn:
            self.memory_conn.close()
            self.memory_conn = None
            print("🔒 内存数据库连接已关闭")
            
        if self.file_conn:
            self.file_conn.close()
            self.file_conn = None
            print("🔒 文件数据库连接已关闭")
    
    def execute_query(self, query: str, params: Optional[List] = None, 
                      use_memory_db: bool = True) -> List[Dict[str, Any]]:
        """执行SQL查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            use_memory_db: 是否使用内存数据库，False则使用文件数据库
            
        Returns:
            查询结果列表
        """
        conn = self.memory_conn if use_memory_db else self.file_conn
        db_type = "内存" if use_memory_db else "文件"
        
        if not conn:
            raise RuntimeError(f"未连接到{db_type}数据库")
        
        try:
            if params:
                result = conn.execute(query, params)
            else:
                result = conn.execute(query)
            
            # 转换为字典列表
            return result.fetchdf().to_dict('records')
        except Exception as e:
            print(f"❌ {db_type}数据库查询执行失败: {e}")
            raise
    
    def get_tables(self, use_memory_db: bool = True) -> List[str]:
        """获取所有表名
        
        Args:
            use_memory_db: 是否使用内存数据库，False则使用文件数据库
            
        Returns:
            表名列表
        """
        result = self.execute_query("SHOW TABLES", use_memory_db=use_memory_db)
        return [row['name'] for row in result]
    
    def get_table_schema(self, table_name: str, use_memory_db: bool = True) -> List[Dict[str, Any]]:
        """获取表结构
        
        Args:
            table_name: 表名
            use_memory_db: 是否使用内存数据库，False则使用文件数据库
            
        Returns:
            表结构信息
        """
        return self.execute_query(f"PRAGMA table_info('{table_name}')", use_memory_db=use_memory_db)

def main():
    """主函数"""
    print("🔌 NC-Link V2 DuckDB连接程序")
    print("=" * 50)
    
    # 检查数据库文件
    db_path = "./nclink_v2.duckdb"
    if not Path(db_path).exists():
        alt_paths = [
            "nclink_v2.duckdb",
            "C:/nlk2/nclink_v2.duckdb",
            "../nclink_v2.duckdb"
        ]
        
        for path in alt_paths:
            if Path(path).exists():
                db_path = path
                break
    
    # 创建连接器
    connector = NcLinkDBConnector(
        file_db_path=db_path,
        memory_db_name=":memory:nc-link-v2"
    )
    
    try:
        # 连接数据库
        memory_ok, file_ok = connector.connect()
        
        # 处理内存数据库
        if memory_ok:
            print("\n📊 内存数据库信息:")
            memory_tables = connector.get_tables(use_memory_db=True)
            print(f"  表数量: {len(memory_tables)}")
            
            if memory_tables:
                print("  表列表:")
                for i, table in enumerate(memory_tables, 1):
                    print(f"    {i}. {table}")
                
                # 选择一个表查看结构和数据
                sample_table = memory_tables[0]
                print(f"\n  表 '{sample_table}' 结构:")
                schema = connector.get_table_schema(sample_table, use_memory_db=True)
                for col in schema:
                    print(f"    - {col['name']} ({col['type']})")
                
                # 查询示例数据
                sample_data = connector.execute_query(
                    f"SELECT * FROM {sample_table} LIMIT 3", 
                    use_memory_db=True
                )
                
                print(f"\n  表 '{sample_table}' 示例数据 (最多3条):")
                for i, row in enumerate(sample_data, 1):
                    print(f"    行 {i}: {row}")
        
        # 处理文件数据库
进程已结束，退出代码为 -1073741819 (0xC0000005)

            file_tables = connector.get_tables(use_memory_db=False)
            print(f"  表数量: {len(file_tables)}")
            
            if file_tables:
                print("  表列表:")
                for i, table in enumerate(file_tables, 1):
                    print(f"    {i}. {table}")
                
                # 选择一个表查看结构和数据
                sample_table = file_tables[0]
                print(f"\n  表 '{sample_table}' 结构:")
                schema = connector.get_table_schema(sample_table, use_memory_db=False)
                for col in schema:
                    print(f"    - {col['name']} ({col['type']})")
                
                # 查询示例数据
                sample_data = connector.execute_query(
                    f"SELECT * FROM {sample_table} LIMIT 3", 
                    use_memory_db=False
                )
                
                print(f"\n  表 '{sample_table}' 示例数据 (最多3条):")
                for i, row in enumerate(sample_data, 1):
                    print(f"    行 {i}: {row}")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
    finally:
        connector.close()
    
    print("\n🎉 操作完成!")

if __name__ == "__main__":
    main()